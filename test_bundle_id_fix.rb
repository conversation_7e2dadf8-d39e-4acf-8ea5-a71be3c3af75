#!/usr/bin/env ruby

# Test script to verify bundle_id type handling
require_relative 'config/environment'

puts "Testing bundle_id type handling fix..."

# Test with integer bundle_id (iOS app)
puts "\n1. Testing with integer bundle_id (iOS)..."
integer_bundle_id = 284910350

begin
  # Test SensorTower::Client normalization
  client = SensorTower::Client.new
  puts "✓ SensorTower client created"
  
  # Test the normalization in get_apps_data
  normalized_ids = Array([integer_bundle_id]).map(&:to_s)
  puts "✓ Bundle ID normalized: #{integer_bundle_id} -> #{normalized_ids.first} (#{normalized_ids.first.class})"
  
rescue => e
  puts "✗ Error: #{e.message}"
end

# Test with string bundle_id (Android app)
puts "\n2. Testing with string bundle_id (Android)..."
string_bundle_id = "com.example.app"

begin
  normalized_ids = Array([string_bundle_id]).map(&:to_s)
  puts "✓ Bundle ID normalized: #{string_bundle_id} -> #{normalized_ids.first} (#{normalized_ids.first.class})"
rescue => e
  puts "✗ Error: #{e.message}"
end

# Test AppDataAdapter with mixed types
puts "\n3. Testing AppDataAdapter with different bundle_id types..."

sample_data = {
  "apps" => [
    {
      "app_id" => 284910350,  # Integer app_id
      "name" => "Test iOS App"
    },
    {
      "app_id" => "com.example.app",  # String app_id
      "name" => "Test Android App"
    }
  ]
}

begin
  # Test with integer bundle_id
  adapter1 = SensorTower::AppDataAdapter.new(sample_data, 284910350, "ios")
  puts "✓ AppDataAdapter works with integer bundle_id: #{adapter1.name}"
  
  # Test with string bundle_id
  adapter2 = SensorTower::AppDataAdapter.new(sample_data, "com.example.app", "android")
  puts "✓ AppDataAdapter works with string bundle_id: #{adapter2.name}"
  
  # Test with string version of integer
  adapter3 = SensorTower::AppDataAdapter.new(sample_data, "284910350", "ios")
  puts "✓ AppDataAdapter works with string version of integer: #{adapter3.name}"
  
rescue => e
  puts "✗ AppDataAdapter error: #{e.message}"
end

# Test with real mobile app data
puts "\n4. Testing with real mobile app data..."

mobile_app = MobileApp.first
if mobile_app
  puts "Found mobile app: #{mobile_app.name}"
  puts "Bundle ID: #{mobile_app.bundle_id} (#{mobile_app.bundle_id.class})"
  puts "Platform: #{mobile_app.platform}"
  
  # Test bundle_id normalization
  normalized_id = mobile_app.bundle_id.to_s
  puts "Normalized bundle_id: #{normalized_id} (#{normalized_id.class})"
else
  puts "No mobile apps found in database"
end

puts "\n✓ Bundle ID type handling test completed!"
puts "\nThe fix ensures:"
puts "1. All bundle_ids are converted to strings before API calls"
puts "2. String comparison works regardless of original type"
puts "3. No more 'strip' method errors on integers"
