module Bedrock
  class Service
    attr_reader :client

    def self.sanitize_filename(filename)
      # Extract everything before the file extension (last dot)
      name_without_extension = File.basename(filename, File.extname(filename))

      # Transliterate and remove all non-alphanumeric characters
      ActiveSupport::Inflector.transliterate(name_without_extension).gsub(/[^0-9A-Za-z]/, "")
    end

    def initialize(region: "us-west-2")
      @client = Aws::BedrockRuntime::Client.new(
        region: region,
        # credentials: Aws::InstanceProfileCredentials.new
      )
    end

    include Bedrock::Embedding
    include Bedrock::FactsExtractor
    include Bedrock::EmailParser
    include Bedrock::AnswerGenerator
    include Bedrock::GenerateWorkspaceLayout
    include Bedrock::PartnerParser
    include Bedrock::ClientParser
    include Bedrock::ClientPartnerLinker
  end
end
