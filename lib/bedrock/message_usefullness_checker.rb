module Bedrock
  module MessageUsefullnessChecker
    include Bedrock::ToolConversation

    def check_message_usefullness(message)
      tool_config = {
        tools: [
          {
            tool_spec: {
              name: "message_usefullness_checker",
              description: "Check if the provided content is informative to provide meaning information to answer business related questions.",
              input_schema: {
                json: {
                  type: "object",
                  properties: {
                    is_useful: {
                      type: "boolean"
                    }
                  },
                  required: [ "is_useful" ]
                }
              }
            }
          }
        ]
      }

      messages = [
        {
          role: "user",
          content: [
            { text: "target message: #{message}" }
          ]
        }
      ]

      response = converse_with_tool(
        model_id: Bedrock::Models::ALL[:meta_scout_7b],
        messages: messages,
        inference_config: {
          temperature: 0,
          top_p: 0.95,
          max_tokens: 8192
        },
        tool_config: tool_config,
        source: "message_usefullness_checker"
      )

      response.dig("output", "message", "content", 0, "tool_use", "input", "is_useful")
    rescue StandardError => e
      puts "Error checking message usefulness: #{e.message}"
      false
    end
  end
end
