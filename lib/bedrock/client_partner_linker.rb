module Bedrock
  module ClientPartnerLinker
    include Bedrock::ToolConversation

    def link_to_client_or_partner(content)
      # Default result structure
      default_result = { "linked_partner" => 0, "linked_client" => 0 }

      tool_config = {
        tools: [
          {
            tool_spec: {
              name: "client_partner_linker",
              description: "Check if the provided content is related to a client or partner.",
              input_schema: {
                json: {
                  type: "object",
                  properties: {
                    linked_partner: {
                      type: "integer",
                      description: "ID of the linked partner, 0 if not applicable"
                    },
                    linked_client: {
                      type: "integer",
                      description: "ID of the linked client, 0 if not applicable"
                    }
                  },
                  required: [ "linked_partner", "linked_client" ]
                }
              }
            }
          }
        ]
      }

      system = [
        {
          text: "Only link to a client or partner if the content clearly mentions the client or partner name. Always return both linked_partner and linked_client fields, using 0 for non-applicable values."
        }
      ]

      messages = [
        {
          role: "user",
          content: [
            { text: "Active clients: #{Client.active.pluck(:id, :name).map { |id, name| "#{id}: #{name}" }.join(', ')}" },
            { text: "Active partners: #{Partner.active.pluck(:id, :name).map { |id, name| "#{id}: #{name}" }.join(', ')}" },
            { text: "Content to check: #{content}" }
          ]
        }
      ]

      response = converse_with_tool(
        model_id: Bedrock::Models::ALL[:meta_maverick_17b],
        messages: messages,
        system: system,
        tool_config: tool_config,
        inference_config: {
          temperature: 0,
          top_p: 0.95,
          max_tokens: 8192
        },
        source: "client_partner_linker"
      )

      if response["output"].nil? || response["output"]["message"].nil?
        puts "No valid response from the model."
        return default_result
      end

      tool_use = response.dig("output", "message", "content", 0, "tool_use")
      return default_result unless tool_use && tool_use["input"]

      result = tool_use["input"]

      # Ensure both keys exist with default values
      result = default_result.merge(result || {})

      # Validate that values are integers and default to 0 if not
      result["linked_partner"] = result["linked_partner"].is_a?(Integer) ? result["linked_partner"] : 0
      result["linked_client"] = result["linked_client"].is_a?(Integer) ? result["linked_client"] : 0

      result
    rescue StandardError => e
      puts "Error linking to client or partner: #{e.message}"
      default_result
    end
  end
end
