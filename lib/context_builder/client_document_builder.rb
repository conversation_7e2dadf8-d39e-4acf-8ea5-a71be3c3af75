module ContextBuilder
  class ClientDocumentBuilder
    attr_reader :client_document
    def initialize(client_document)
      @client_document = client_document
    end

    def build
      # Check if the document has AI access enabled, Google Sheets URL, and sheet names
      return nil unless client_document.assistant_read? &&
                       google_sheets_url?(client_document.url) &&
                       client_document.assistant_read_sheet_names.present? &&
                       client_document.assistant_read_sheet_names.split(",").any?

      sheets_sections = []

      begin
        spreadsheet_id = extract_spreadsheet_id(client_document.url)
        return nil unless spreadsheet_id

        google_service = GoogleSheetsService.new(spreadsheet_id)

        client_document.assistant_read_sheet_names.split(",").each do |sheet_name|
          sheet_name = sheet_name.strip
          next if sheet_name.blank?

          begin
            sheet_data = google_service.read_values(sheet_name)

            if sheet_data&.any?
              sheets_sections << build_sheet_data_table(client_document.title, sheet_name, sheet_data)
            else
              sheets_sections << build_empty_sheet_notice(client_document.title, sheet_name)
            end
          rescue => e
            Rails.logger.error "Error reading sheet '#{sheet_name}' from document '#{client_document.title}': #{e.message}"
            sheets_sections << build_sheet_error_notice(client_document.title, sheet_name, e.message)
          end
        end
      rescue => e
        Rails.logger.error "Error processing Google Sheets document '#{client_document.title}': #{e.message}"
        sheets_sections << build_document_error_notice(client_document.title, e.message)
      end

      return nil if sheets_sections.empty?

      <<~MARKDOWN
        ### Google Sheets Data

        #{sheets_sections.join("\n\n")}
      MARKDOWN
    end

    def google_sheets_url?(url)
      return false if url.blank?
      url.include?("docs.google.com/spreadsheets") || url.include?("sheets.google.com")
    end

    def extract_spreadsheet_id(url)
      return nil if url.blank?

      # Handle different Google Sheets URL formats
      patterns = [
        %r{/spreadsheets/d/([a-zA-Z0-9-_]+)},
        %r{/spreadsheets/u/\d+/d/([a-zA-Z0-9-_]+)},
        %r{key=([a-zA-Z0-9-_]+)},
        %r{id=([a-zA-Z0-9-_]+)}
      ]

      patterns.each do |pattern|
        match = url.match(pattern)
        return match[1] if match
      end

      nil
    end

    def build_sheet_data_table(doc_title, sheet_name, sheet_data)
      return build_empty_sheet_notice(doc_title, sheet_name) if sheet_data.empty?

      # Filter out completely empty rows
      filtered_data = sheet_data.reject { |row| row.nil? || row.empty? || row.all?(&:blank?) }
      return build_empty_sheet_notice(doc_title, sheet_name) if filtered_data.empty?

      # Limit rows for display
      display_data = filtered_data

      # Find the maximum number of columns across all rows
      max_columns = display_data.map(&:length).max

      # Build table with generic column headers
      headers = (1..max_columns).map { |i| "#{i}" }
      header_row = "| " + headers.join(" | ") + " |"
      separator_row = "|" + headers.map { "---" }.join("|") + "|"

      # Build data rows
      table_rows = display_data.map do |row|
        row_values = (0...max_columns).map { |index| format_cell_value(row[index]) }
        "| " + row_values.join(" | ") + " |"
      end

      <<~TABLE
        #### #{client_document.client.name} - #{doc_title} - #{sheet_name}
        **Rows:** #{display_data.length} | **Columns:** #{max_columns}

        #{header_row}
        #{separator_row}
        #{table_rows.join("\n")}
      TABLE
    end

    def build_empty_sheet_notice(doc_title, sheet_name)
      <<~NOTICE
        #### #{client_document.client.name} - #{doc_title} - #{sheet_name}
        *No data available in this sheet.*
      NOTICE
    end

    def build_sheet_error_notice(doc_title, sheet_name, error_message)
      <<~NOTICE
        #### #{client_document.client.name} - #{doc_title} - #{sheet_name}
        *Error reading sheet data: #{error_message}*
      NOTICE
    end

    def build_document_error_notice(doc_title, error_message)
      <<~NOTICE
        #### #{client_document.client.name} - #{doc_title}
        *Error accessing Google Sheets document: #{error_message}*
      NOTICE
    end

    def format_cell_value(value)
      return "" if value.nil?

      # Truncate long values to prevent table overflow
      str_value = value.to_s.strip
      return str_value if str_value.length <= 50

      "#{str_value[0..47]}..."
    end
  end
end
