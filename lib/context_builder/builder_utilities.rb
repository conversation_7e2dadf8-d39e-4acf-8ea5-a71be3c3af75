module ContextBuilder
  module BuilderUtilities
    def format_currency(amount)
      return "0" if amount.nil? || amount == 0
      amount.round(2).to_s
    end

    def sanitize_for_markdown(text)
      return nil if text.nil?
      text.gsub(/\r\n|\r|\n/, " ")
    end

    def calculate_growth(previous, current)
      return "New" if previous == 0 && current > 0
      return "0%" if previous == 0 && current == 0
      return "-100%" if previous > 0 && current == 0

      growth = ((current - previous) / previous * 100).round(1)
      growth >= 0 ? "+#{growth}%" : "#{growth}%"
    end

    def parse_currency_for_sorting(currency_string)
      return 0 if currency_string == "0"

      if currency_string.end_with?("M")
        currency_string.gsub(/[M$]/, "").to_f * 1_000_000
      elsif currency_string.end_with?("K")
        currency_string.gsub(/[K$]/, "").to_f * 1_000
      else
        currency_string.gsub("$", "").to_f
      end
    end

    def number_to_human_size(bytes)
      return "0 B" if bytes == 0

      units = %w[B KB MB GB TB]
      exp = (Math.log(bytes) / Math.log(1024)).to_i
      exp = [ exp, units.length - 1 ].min

      "%.1f %s" % [ bytes.to_f / (1024 ** exp), units[exp] ]
    end

    def status_emoji(status)
      case status
      when "running", "active" then "🟢"
      when "paused" then "🟡"
      when "archived", "inactive" then "🔴"
      when "onboarding" then "🔵"
      else "⚪"
      end
    end

    def boolean_indicator(value)
      value ? "✅" : "❌"
    end

    def format_date_range(start_date, end_date = nil)
      if end_date
        "#{start_date} - #{end_date}"
      else
        "#{start_date} - Ongoing"
      end
    end

    # Shared Gong activity builder
    def build_gong_activity_table(gong_calls)
      return nil if gong_calls.empty?

      calls_data = gong_calls.map do |call|
        duration = call.duration ? "#{(call.duration / 60).round}min" : "Unknown"
        transcript_status = call.has_transcript? ? "✅" : "❌"
        transcript_count = call.gong_transcripts.count

        "| #{sanitize_for_markdown(call.title)} | #{call.started_at&.strftime('%Y-%m-%d %H:%M') || 'Unknown'} | #{duration} | #{transcript_status} (#{transcript_count} segments) |"
      end

      <<~TABLE
        ### Recent Gong Calls (#{gong_calls.count})
        | Call Title | Date | Duration | Transcript |
        |------------|------|----------|------------|
        #{calls_data.join("\n")}
      TABLE
    end

    def build_gong_transcripts(gong_calls)
      calls_with_transcripts = gong_calls.select(&:has_transcript?)
      return nil if calls_with_transcripts.empty?

      transcript_sections = calls_with_transcripts.map do |call|
        transcripts = call.gong_transcripts.where(is_useful: true).order(:start_time)
        speaker_abbrevs = {}

        transcript_content = transcripts.map do |transcript|
          speaker_name = sanitize_for_markdown(transcript.speaker_name) || "Unknown"

          # Create abbreviation and track speakers
          if !speaker_abbrevs[speaker_name]
            abbrev = speaker_name.split.map(&:first).join.upcase
            speaker_abbrevs[speaker_name] = abbrev
            speaker_label = "#{speaker_name} (#{abbrev})"
          else
            speaker_label = "#{speaker_abbrevs[speaker_name]}"
          end

           "#{speaker_label}: #{sanitize_for_markdown(transcript.text)}"
        end.join("\n")

        <<~TRANSCRIPT
          #### #{sanitize_for_markdown(call.title)} - #{call.started_at&.strftime('%Y-%m-%d %H:%M')}
          #{call.gong_url ? "[View in Gong](#{call.gong_url})" : ""}

          #{transcript_content}
        TRANSCRIPT
      end

      sections = [ "### Call Transcripts" ]
      sections.concat(transcript_sections)
      sections.join("\n\n")
    end

    # Shared Slack activity builder
    def build_slack_activity_table(slack_messages)
      return nil if slack_messages.empty?

      messages_data = slack_messages.map do |message|
        user_name = sanitize_for_markdown(message.message_user_name) || "Unknown User"
        permalink = message.permalink ? "[View](#{message.permalink})" : "No link"

        "| #{user_name} | #{message.message_time.strftime('%Y-%m-%d %H:%M')} | #{sanitize_for_markdown(message.message_text)} | #{permalink} |"
      end

      <<~TABLE
        ### Recent Slack Messages (#{slack_messages.count})
        | User | Date | Message | Link |
        |------|------|---------|------|
        #{messages_data.join("\n")}
      TABLE
    end

    # Shared Fireflies activity builder
    def build_fireflies_activity_table(fireflies_meetings)
      return nil if fireflies_meetings.empty?

      meetings_data = fireflies_meetings.map do |meeting|
        duration = meeting.duration ? "#{(meeting.duration / 60).round}min" : "Unknown"
        utterance_count = meeting.fireflies_utterances.count
        has_transcript = utterance_count > 0 ? "✅" : "❌"

        "| #{sanitize_for_markdown(meeting.title)} | #{meeting.meeting_date&.strftime('%Y-%m-%d %H:%M') || 'Unknown'} | #{duration} | #{has_transcript} (#{utterance_count} utterances) |"
      end

      <<~TABLE
        ### Recent Fireflies Meetings (#{fireflies_meetings.count})
        | Meeting Title | Date | Duration | Transcript |
        |---------------|------|----------|------------|
        #{meetings_data.join("\n")}
      TABLE
    end

    def build_fireflies_transcripts(fireflies_meetings)
      meetings_with_transcripts = fireflies_meetings.select { |m| m.fireflies_utterances.any? }
      return nil if meetings_with_transcripts.empty?

      transcript_sections = meetings_with_transcripts.map do |meeting|
        utterances = meeting.fireflies_utterances.where(is_useful: true).order(:sentence_index)
        speaker_abbrevs = {}

        transcript_content = utterances.map do |utterance|
          speaker_name = sanitize_for_markdown(utterance.speaker_name) || sanitize_for_markdown(utterance.speaker_id) || "Unknown Speaker"

          # Create abbreviation and track speakers
          if !speaker_abbrevs[speaker_name]
            abbrev = speaker_name.split.map(&:first).join.upcase
            speaker_abbrevs[speaker_name] = abbrev
            speaker_label = "#{speaker_name} (#{abbrev})"
          else
            speaker_label = "#{speaker_abbrevs[speaker_name]}"
          end

          "#{speaker_label}: #{sanitize_for_markdown(utterance.content)}"
        end.join("\n")

        <<~TRANSCRIPT
          #### #{sanitize_for_markdown(meeting.title)} - #{meeting.meeting_date&.strftime('%Y-%m-%d %H:%M')}
          #{meeting.meeting_url ? "[View in Fireflies](#{meeting.meeting_url})" : ""}

          #{transcript_content}
        TRANSCRIPT
      end

      sections = [ "### Meeting Transcripts" ]
      sections.concat(transcript_sections)
      sections.join("\n\n")
    end
  end
end
