module FeatureEncoding<PERSON>el<PERSON>
  def one_hot_encode_os(os, encoding_categories = nil)
    os_types = encoding_categories&.dig("os_types") || [ "ios", "android", "mobile web", "universal" ]
    normalized_os = normalize_os(os)
    os_types.map { |type| normalized_os == type ? 1.0 : 0.0 }
  end

  def one_hot_encode_country_code(country_code, encoding_categories = nil)
    codes = encoding_categories&.dig("country_codes") || get_country_codes
    return [ 0.0 ] * codes.length if country_code.blank? || !codes.include?(country_code)
    codes.map { |code| country_code == code ? 1.0 : 0.0 }
  end

  def one_hot_encode_app_category(category, encoding_categories = nil)
    categories = encoding_categories&.dig("app_categories") || get_app_categories
    return [ 0.0 ] * categories.length if category.blank? || category == "unknown" || !categories.include?(category)
    categories.map { |cat| category == cat ? 1.0 : 0.0 }
  end

  def one_hot_encode_partner_category(category, encoding_categories = nil)
    categories = encoding_categories&.dig("partner_categories") || get_partner_categories
    return [ 0.0 ] * categories.length if category.blank? || category == "unknown" || !categories.include?(category)
    categories.map { |cat| category == cat ? 1.0 : 0.0 }
  end

  def one_hot_encode_track_party(track_party, encoding_categories = nil)
    parties = encoding_categories&.dig("track_parties") || get_track_parties
    return [ 0.0 ] * parties.length if track_party.blank? || !parties.include?(track_party)
    parties.map { |tp| track_party == tp ? 1.0 : 0.0 }
  end

  def one_hot_encode_partner_id(partner_id, encoding_categories = nil)
    ids = encoding_categories&.dig("partner_ids") || get_partner_ids
    return [ 0.0 ] * ids.length if partner_id.blank? || !ids.include?(partner_id.to_s)
    ids.map { |id| partner_id.to_s == id.to_s ? 1.0 : 0.0 }
  end

  def one_hot_encode_client_id(client_id, encoding_categories = nil)
    ids = encoding_categories&.dig("client_ids") || get_client_ids
    return [ 0.0 ] * ids.length if client_id.blank? || !ids.include?(client_id.to_s)
    ids.map { |id| client_id.to_s == id.to_s ? 1.0 : 0.0 }
  end

  def one_hot_encode_month(month)
    return [ 0.0 ] * 12 if month.blank?
    (1..12).map { |m| month == m ? 1.0 : 0.0 }
  end

  def one_hot_encode_day_of_week(day_of_week)
    return [ 0.0 ] * 7 if day_of_week.blank?
    (0..6).map { |d| day_of_week == d ? 1.0 : 0.0 }
  end

  def normalize_os(os)
    return "unknown" if os.blank?
    normalized = os.downcase.strip
    case normalized
    when /ios/ then "ios"
    when /android/ then "android"
    when /mobile web/, /web/ then "mobile web"
    when /universal/ then "universal"
    else "unknown"
    end
  end

  private

  def get_country_codes
    @country_codes ||= CampaignSpendSummary.joins(:campaign).distinct.pluck("campaigns.country_code").compact.sort
  end

  def get_app_categories
    @app_categories ||= MobileAppCategory.pluck(:name).sort
  end

  def get_partner_categories
    @partner_categories ||= PartnerCategory.pluck(:name).sort
  end

  def get_track_parties
    @track_parties ||= CampaignSpendSummary.joins(:click_url, click_url: :track_party).distinct.pluck("track_parties.name").compact.sort
  end

  def get_partner_ids
    @partner_ids ||= CampaignSpendSummary.distinct.pluck(:legacy_partner_id).compact.sort
  end

  def get_client_ids
    @client_ids ||= CampaignSpendSummary.distinct.pluck(:legacy_client_id).compact.sort
  end
end
