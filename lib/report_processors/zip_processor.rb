require "open-uri"
require "zip"
require "stringio"

module ReportProcessors
  class ZipProcessor
    def self.process_url(url, &block)
      new.process_url(url, &block)
    end

    def process_url(url)
      retries = 0
      max_retries = 3
      encoded_url = URI::DEFAULT_PARSER.escape(url.strip)
      http = HTTPX.plugin(:retries, :retry_after)
      http.max_retries(max_retries)
        .get(encoded_url, retry_after: ->(*) { (retries += 1) * 2 })
        .raise_for_status
        .tap do |response|
          zip_content = StringIO.new(response.body.to_s)
          process_zip_content(zip_content) { |content| yield content }
        end
    rescue HTTPX::ConnectionError => e
      Rails.logger.error("SSL connection reset error: #{e.message} [URL: #{url}]")
      raise
    rescue HTTPX::TimeoutError => e
      Rails.logger.error("Request timeout: #{e.message}")
      raise
    rescue => e
      Rails.logger.error("Unexpected error: #{e.class} - #{e.message}")
      raise
    end

    private

    def process_zip_content(zip_content)
      Zip::File.open_buffer(zip_content) do |zip_file|
        zip_file.each do |entry|
          next unless entry.name.end_with?(".csv")
          content = entry.get_input_stream.read.force_encoding("UTF-8")
          first_line = content.lines.first.to_s.strip
          next if first_line.empty? || first_line.match?(/<\/?[a-z][\s\S]*>/i) || !first_line.match?(/[,;\t|]/)

          yield content
        end
      end
    end
  end
end
