# config/initializers/ai_config.rb

module AiConfig
  class << self
    def configuration
      @configuration ||= Configuration.new
    end

    def configure
      yield(configuration)
    end
  end

  class Configuration
    attr_accessor :assistants, :model_configs

    def initialize
      @assistants = load_assistants.with_indifferent_access
      @model_configs = load_model_configs.with_indifferent_access
    end

    private

    def load_assistants
      {
        default: {
          tools: []
        },
        feedmob: {
          system_prompt: "You are FeedMob's ad ops assistant. Always use math_evaluator for any calculations - never calculate mentally.",
          tools: [
            "search_partners",
            "search_clients",
            "list_campaign_spends",
            "list_campaign_pricing_configurations",
            "generate_chart",
            "generate_file",
            "load_adops_context",
            "read_spreadsheet",
            "math_evaluator"
          ]
        },
        dev: {
          tools: [
            "create_github_issue",
            "comment_github_issue",
            "load_github_issue"
          ]
        },
        experiment: {
          tools: [
            "generate_typst_report"
          ]
        }
      }
    end

    def load_model_configs
      {
        meta_maverick_17b: {
          region: "us-west-2",
          model_id: "us.meta.llama4-maverick-17b-instruct-v1:0",
          pricing: {
            input_tokens: 0.00000024,
            output_tokens: 0.00000097
          },
          inference_profiles: {
            default: {
              config: {
                temperature: 0.8,
                top_p: 0.95,
                max_tokens: 8192
              }
            }
          }
        },
        claude_4_0_sonnet: {
          region: "us-west-2",
          model_id: "us.anthropic.claude-sonnet-4-20250514-v1:0",
          pricing: {
            input_tokens: 0.000003,  # Cost per input token in USD
            output_tokens: 0.000015  # Cost per output token in USD
          },
          inference_profiles: {
            default: {
              config: {
                max_tokens: 30000,
                temperature: 0.8,
                top_p: 0.999,
                stop_sequences: []
              },
              additional_config: {
                top_k: 250
              }
            }
          }
        },
        claude_3_7_sonnet: {
          region: "us-west-2",
          model_id: "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
          pricing: {
            input_tokens: 0.000003,  # Cost per input token in USD
            output_tokens: 0.000015  # Cost per output token in USD
          },
          inference_profiles: {
            default: {
              config: {
                max_tokens: 10000,
                temperature: 0.8,
                top_p: 0.999,
                stop_sequences: []
              },
              additional_config: {
                top_k: 250
              }
            }
          }
        },
        claude_3_7_sonnet_thinking: {
          region: "us-west-2",
          model_id: "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
          pricing: {
            input_tokens: 0.000003,  # Cost per input token in USD
            output_tokens: 0.000015  # Cost per output token in USD
          },
          inference_profiles: {
            default: {
              config: {
                max_tokens: 10000,
                stop_sequences: []
              },
              additional_config: {
                thinking: {
                  type: "enabled",
                  budget_tokens: 5000
                }
              }
            }
          }
        },
        claude_3_5_sonnet: {
          region: "us-west-2",
          model_id: "anthropic.claude-3-5-sonnet-20241022-v2:0",
          pricing: {
            input_tokens: 0.000003,  # Cost per input token in USD
            output_tokens: 0.000015  # Cost per output token in USD
          },
          inference_profiles: {
            default: {
              config: {
                max_tokens: 4096,
                temperature: 0.8,
                top_p: 0.999,
                stop_sequences: []
              },
              additional_config: {
                top_k: 250
              }
            }
          }
        },
        claude_3_5_haiku: {
          region: "us-west-2",
          model_id: "anthropic.claude-3-5-haiku-20241022-v1:0",
          pricing: {
            input_tokens: 0.0000008,  # Cost per input token in USD
            output_tokens: 0.000004  # Cost per output token in USD
          },
          inference_profiles: {
            default: {
              config: {
                max_tokens: 4096,
                temperature: 0.8,
                top_p: 0.999,
                stop_sequences: []
              },
              additional_config: {
                top_k: 250
              }
            }
          }
        },
        nova_pro: {
          region: "us-east-1",
          model_id: "amazon.nova-pro-v1:0",
          pricing: {
            input_tokens: 0.0000008,  # Cost per input token in USD
            output_tokens: 0.0000032  # Cost per output token in USD
          },
          inference_profiles: {
            default: {
              config: {}
            }
          }
        }
      }
    end
  end
end
