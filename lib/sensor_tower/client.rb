require "json"

module SensorTower
  class Client
    class APIError < StandardError; end

    attr_reader :api_key

    BASE_URL = "https://api.sensortower.com/v1".freeze

    def initialize
      @api_key = "ST0_Dpkx4Kxd_appC33PNTNs5h6" || Rails.application.credentials.sensor_tower[:api_key]
    end

    def get_ios_apps(app_ids, options = {})
      get_apps_data("ios", app_ids, options)
    end

    def get_android_apps(app_ids, options = {})
      get_apps_data("android", app_ids, options)
    end

    private

    def get_apps_data(platform, app_ids, options = {})
      # Ensure all app_ids are strings to avoid strip errors
      normalized_app_ids = Array(app_ids).map(&:to_s)

      params = {
        app_ids: normalized_app_ids,
        country: options[:country] || "US"
      }

      get("/#{platform}/apps", params)
    end

    def get(endpoint, params = {})
      url = "#{BASE_URL}#{endpoint}"
      query_params = params.merge(auth_token: api_key)

      headers = {
        "Content-Type" => "application/json",
        "User-Agent" => "FeedMob Assistant/1.0"
      }

      response = HTTPX.get(url, params: query_params.to_h, headers: headers)

      case response.status
      when 200
        JSON.parse(response.body)
      else
        raise SensorTower::APIError, "API request failed: #{response.status} - #{response.body}"
      end
    end
  end
end
