class CreateSubClientBudget < ActiveRecord::Migration[8.1]
  def change
    create_table :sub_client_budgets do |t|
      t.references :client_budget, null: false, foreign_key: true
      t.string :name
      t.decimal :amount, precision: 12, scale: 2

      t.timestamps
    end

    create_table :sub_client_budget_campaigns do |t|
      t.references :sub_client_budget, null: false, foreign_key: true
      t.references :campaigns, null: false

      t.timestamps
    end
  end
end
