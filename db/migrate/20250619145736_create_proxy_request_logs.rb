class CreateProxyRequestLogs < ActiveRecord::Migration[8.1]
  def change
    create_table :proxy_request_logs do |t|
      t.references :api_playground, null: false, foreign_key: true
      t.string :request_url
      t.string :request_method
      t.jsonb :request_headers
      t.jsonb :request_body
      t.integer :response_status
      t.jsonb :response_headers
      t.text :response_body
      t.boolean :succeeded
      t.string :error_message
      t.datetime :requested_at

      t.timestamps
    end
  end
end
