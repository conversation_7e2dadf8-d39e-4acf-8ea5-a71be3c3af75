production:
  sync_external_users:
    class: SyncExternal::UsersJob
    schedule: every day at midnight
  sync_external_clients:
    class: SyncExternal::ClientsJob
    schedule: every day at midnight
  sync_external_partners:
    class: SyncExternal::PartnersJob
    schedule: every day at midnight
  sync_external_track_parties:
    class: SyncExternal::TrackPartiesJob
    schedule: every day at midnight
  sync_external_client_budgets:
    class: SyncExternal::ClientBudgetsJob
    schedule: every day at 0:20am
  sync_firefile_transactions:
    class: SyncFirefliesJob
    schedule: every day at 0:25am
  load_aiobserver_emails:
    class: ParseDocument::GmailJob
    args: ["<EMAIL>"]
    schedule: every day at 0:30am
  sync_active_status:
    class: SyncActiveStatusJob
    schedule: every day at 0:30am and 15:00pm
  sync_gong_calls:
    class: GongCallSyncJob
    schedule: every day at 0:35am
  link_messgae_to_client_partners:
    class: LinkToClientPartnerJob
    schedule: every day at 0:41am
  parse_aiobserver_emails:
    class: BatchParseEmailThreadJob
    schedule: every day at 0:45am
  sync_external_mobile_apps:
    class: SyncExternal::MobileAppsJob
    schedule: every day at 0:30am
  track_mobile_app_metrics:
    class: TrackMobileAppMetricsJob
    schedule: every day at 0:40am
  sync_external_campaigns:
    class: SyncExternal::CampaignsJob
    schedule: every day at 0:45am
  sync_external_click_urls:
    class: SyncExternal::ClickUrlsJob
    schedule: every day at 1:00am
  sync_mmp_events:
    class: SyncExternal::MmpEventsJob
    schedule: every day at 1:05am
  sync_redshift_impression_and_click:
    class: SyncExternal::ImpressionsAndClicksJob
    schedule: every day at 1:10am
  sync_net_spend_events:
    class: SyncExternal::NetSpendEventsJob
    schedule: every day at 1:10am
  sync_external_campaign_mappings:
    class: SyncExternal::CampaignMappingsJob
    schedule: every day at 1:15am
  sync_mmp_aggregated_events:
    class: SyncExternal::TrackPartyAggregatedEventsJob
    schedule: every day at 1:15am
  sync_partner_jampp_integrations:
    class: SyncExternal::PartnerJamppIntegrationsJob
    schedule: every day at 1:15am
  sync_agency_conversion_events:
    class: SyncExternal::AgencyConversionEventsJob
    schedule: every day at 1:20am
  refresh_spends_view:
    class: RefreshSpendsViewJob
    schedule: every day at 1:45am
  refresh_campaign_spends_file:
    class: GenerateCampaignSpendsFileJob
    schedule: every day at 2:00am
  verify_campaign_spends:
    class: VerifyData::AllSpendJob
    schedule: every day at 2:25am
  sync_client_reports:
    class: SyncExternal::ClientReportsSyncJob
    schedule: every day at 2:30am
  refresh_yesterday_spend:
    class: RefreshYesterdaySpendsViewJob
    schedule: every day at 9:00am and 14:00pm
  sync_recent_hubspot_tickets:
    class: SyncRecentHubspotTicketsJob
    schedule: every day at 1:00am and 6:00am and 11:00pm
