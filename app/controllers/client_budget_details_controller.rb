class ClientBudgetDetailsController < ApplicationController
  before_action :set_client_budget_detail, only: %i[show edit update destroy]
  before_action :set_client_budget, only: %i[new create]

  def index
    @client_budget_details = ClientBudgetDetail.includes(:client_budget, :campaigns).all
  end

  def show
  end

  def new
    @client_budget_detail = @client_budget.client_budget_details.build
    associated_campaign_ids = @client_budget.client_budget_details
                                           .joins(:budget_associations)
                                           .where(budget_associations: { budgetable_type: "Campaign" })
                                           .pluck("budget_associations.budgetable_id")
    @campaigns = Campaign.where(client: @client_budget.client)
                        .where.not(id: associated_campaign_ids).order(:country_code, :name)
    @countries = @campaigns.pluck(:country_code).compact.uniq.sort
  end

  def edit
    associated_campaign_ids = @client_budget_detail.client_budget.client_budget_details
                                                   .where.not(id: @client_budget_detail.id)
                                                   .joins(:budget_associations)
                                                   .where(budget_associations: { budgetable_type: "Campaign" })
                                                   .pluck("budget_associations.budgetable_id")
    @campaigns = Campaign.where(client: @client_budget_detail.client_budget.client)
                        .where.not(id: associated_campaign_ids).order(:country_code, :name)
    @countries = @campaigns.pluck(:country_code).compact.uniq.sort
  end

  def create
    @client_budget_detail = @client_budget.client_budget_details.build(client_budget_detail_params)

    if @client_budget_detail.save
      if params[:campaign_ids].present?
        params[:campaign_ids].each do |campaign_id|
          next if campaign_id.blank?
          @client_budget_detail.budget_associations.create!(
            budgetable_type: "Campaign",
            budgetable_id: campaign_id
          )
        end
      end

      redirect_to client_path(@client_budget.client), notice: "Budget detail was successfully created."
    else
      associated_campaign_ids = @client_budget.client_budget_details
                                             .joins(:budget_associations)
                                             .where(budget_associations: { budgetable_type: "Campaign" })
                                             .pluck("budget_associations.budgetable_id")
      @campaigns = Campaign.where(client: @client_budget.client)
                          .where.not(id: associated_campaign_ids)
      @countries = @campaigns.pluck(:country_code).compact.uniq.sort
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if @client_budget_detail.update(client_budget_detail_params)
      @client_budget_detail.budget_associations.where(budgetable_type: "Campaign").destroy_all
      if params[:campaign_ids].present?
        params[:campaign_ids].each do |campaign_id|
          next if campaign_id.blank?
          @client_budget_detail.budget_associations.create!(
            budgetable_type: "Campaign",
            budgetable_id: campaign_id
          )
        end
      end

      redirect_to client_path(@client_budget_detail.client_budget.client), notice: "Budget detail was successfully updated."
    else
      associated_campaign_ids = @client_budget_detail.client_budget.client_budget_details
                                                     .where.not(id: @client_budget_detail.id)
                                                     .joins(:budget_associations)
                                                     .where(budget_associations: { budgetable_type: "Campaign" })
                                                     .pluck("budget_associations.budgetable_id")
      @campaigns = Campaign.where(client: @client_budget_detail.client_budget.client)
                          .where.not(id: associated_campaign_ids)
      @countries = @campaigns.pluck(:country_code).compact.uniq.sort
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    client = @client_budget_detail.client_budget.client
    @client_budget_detail.destroy!
    redirect_to client_path(client), notice: "Budget detail was successfully deleted."
  end

  def campaigns_by_country
    client_id = params[:client_id]
    country_code = params[:country_code]

    campaigns = Campaign.where(client_id: client_id, country_code: country_code)
                       .select(:id, :name)

    render json: campaigns.map { |c| { id: c.id, name: c.name } }
  end

  private

  def set_client_budget_detail
    @client_budget_detail = ClientBudgetDetail.find(params[:id])
  end

  def set_client_budget
    @client_budget = ClientBudget.find(params[:client_budget_id])
  end

  def client_budget_detail_params
    params.require(:client_budget_detail).permit(:amount)
  end
end
