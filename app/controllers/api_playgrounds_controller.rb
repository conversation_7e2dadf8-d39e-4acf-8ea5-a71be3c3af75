class ApiPlaygroundsController < ApplicationController
  before_action :set_api_playground, only: [ :show, :edit, :destroy, :save_code ]

  def index
    @api_playgrounds = ApiPlayground.all.order(:name)
  end

  def show
  end

  def new
    @api_playground = ApiPlayground.new
  end

  def create
    @api_playground = ApiPlayground.new(api_playground_params)

    if @api_playground.save
      redirect_to api_playground_path(@api_playground.slug), notice: "API Playground was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    @api_playground = ApiPlayground.find(params[:id])
    if @api_playground.update(api_playground_params)
      redirect_to api_playgrounds_url, notice: "API Playground was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @api_playground.destroy
    redirect_to api_playgrounds_url, notice: "API Playground was successfully deleted."
  end

  def save_code
    if @api_playground.update(code: params[:code])
      render json: { success: true, message: "Code saved successfully" }
    else
      render json: { success: false, message: "Error saving code: #{@api_playground.errors.full_messages.join(', ')}" }, status: :unprocessable_entity
    end
  end

  private

  def set_api_playground
    @api_playground = ApiPlayground.find_by!(slug: params[:id])
  end

  def api_playground_params
    params.require(:api_playground).permit(:name, :slug, :description, :code, :client_id, :partner_id, :track_party_id)
  end
end
