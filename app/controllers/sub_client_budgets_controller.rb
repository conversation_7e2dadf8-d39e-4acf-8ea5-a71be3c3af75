class SubClientBudgetsController < ApplicationController
  before_action :set_client_budget
  before_action :set_sub_client_budget, only: [ :show, :edit, :update, :destroy ]

  def new
    @sub_client_budget = @client_budget.sub_client_budgets.new
  end

  def create
    @sub_client_budget = @client_budget.sub_client_budgets.new(sub_client_budget_params)
    if @sub_client_budget.save
      redirect_to @client_budget, notice: "Sub-budget was successfully created."
    else
      render :new
    end
  end

  def edit
  end

  def update
    if @sub_client_budget.update(sub_client_budget_params)
      redirect_to @client_budget, notice: "Sub-budget was successfully updated."
    else
      render :edit
    end
  end

  def destroy
    @sub_client_budget.destroy
    redirect_to @client_budget, notice: "Sub-budget was successfully destroyed."
  end

  private

  def set_client_budget
    @client_budget = ClientBudget.find(params[:client_budget_id])
  end

  def set_sub_client_budget
    @sub_client_budget = @client_budget.sub_client_budgets.find(params[:id])
  end

  def sub_client_budget_params
    params.require(:sub_client_budget).permit(:name, :amount, sub_client_budget_campaigns_attributes: [ :id, :campaign_id, :_destroy ])
  end
end
