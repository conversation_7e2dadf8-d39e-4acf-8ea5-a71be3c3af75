class ProxyController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [ :fetch ]

  def fetch
    permitted_params = params.permit(:url, :method, :body, headers: {}, params: {})
    target_url = permitted_params[:url]
    target_method = permitted_params[:method]&.upcase || "GET"
    target_headers = permitted_params[:headers] || {}
    target_body = permitted_params[:body] || {}
    target_parameters = permitted_params[:params] || {}

    unless target_url.present?
      return render json: { error: "URL parameter is required" }, status: 400
    end

    begin
      URI.parse(target_url)
    rescue URI::InvalidURIError
      return render json: { error: "Invalid URL format" }, status: 400
    end

    response = make_request(target_url, target_method, target_headers, target_body, target_parameters)

    if response.error
      return render json: { error: "Proxy request failed: #{response.error}" }, status: 500
    end

    render json: {
      status: response.status,
      headers: response.headers,
      body: response.body.to_s
    }
  end

  private

  def make_request(url, method = "GET", custom_headers = {}, body = nil, params = {})
    headers = {
      "User-Agent" => "FeedMob Proxy/1.0",
      "Accept" => "application/json, text/html, */*"
    }.merge(custom_headers)

    options = { headers: headers }
    http = HTTPX.with(options)

    case method.upcase
    when "GET"
      http.get(url, params: params.to_h)
    when "POST"
      http.post(url, body: body)
    else
      raise "Unsupported HTTP method: #{method}"
    end
  end
end
