require "ostruct"

class ProxyController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [ :fetch ]
  before_action :set_api_playground, only: [ :fetch ]
  before_action :check_rate_limit, only: [ :fetch ]

  def fetch
    permitted_params = params.permit(:url, :method, :body, headers: {}, params: {}, api_playground_slug: nil)
    target_url = permitted_params[:url]
    target_method = permitted_params[:method]&.upcase || "GET"
    target_headers = permitted_params[:headers] || {}
    target_body = permitted_params[:body] || {}
    target_parameters = permitted_params[:params] || {}

    unless target_url.present?
      return render json: {
        error: "URL parameter is required",
        error_type: "validation",
        message: "Please provide a valid URL to make the request."
      }, status: 400
    end

    begin
      URI.parse(target_url)
    rescue URI::InvalidURIError
      return render json: {
        error: "Invalid URL format",
        error_type: "validation",
        message: "The provided URL format is invalid. Please check and try again."
      }, status: 400
    end

    log = @api_playground.proxy_request_logs.build(
      request_url: target_url,
      request_method: target_method,
      request_headers: target_headers,
      request_body: target_body,
      requested_at: Time.current
    )

    response = make_request(target_url, target_method, target_headers, target_body, target_parameters)

    if response.error
      log.assign_attributes(
        succeeded: false,
        error_message: response.error.to_s,
        response_status: response.status
      )
      log.save

      error_response = {
        error: response.error,
        error_type: determine_error_type(response),
        message: format_error_message(response),
        status: response.status
      }

      return render json: error_response, status: response.status || 500
    end

    log.assign_attributes(
      succeeded: true,
      response_status: response.status,
      response_headers: response.headers,
      response_body: response.body.to_s
    )
    log.save

    render json: {
      status: response.status,
      headers: response.headers,
      body: response.body.to_s
    }
  end

  private

  def set_api_playground
    @api_playground = ApiPlayground.find_by(slug: params[:api_playground_slug])
    unless @api_playground
      render json: {
        error: "ApiPlayground not found",
        error_type: "not_found",
        message: "The API Playground with slug '#{params[:api_playground_slug]}' could not be found."
      }, status: :not_found
    end
  end

  def check_rate_limit
    return unless @api_playground.rate_limit_count.present? && @api_playground.rate_limit_period.present?

    period_duration = parse_period(@api_playground.rate_limit_period)
    return unless period_duration

    request_count = @api_playground.proxy_request_logs.where("requested_at >= ?", Time.current - period_duration).count

    if request_count >= @api_playground.rate_limit_count
      remaining_time = calculate_remaining_time(period_duration)
      render json: {
        error: "Rate limit exceeded",
        error_type: "rate_limit",
        message: "You have exceeded the rate limit of #{@api_playground.rate_limit_count} requests per #{@api_playground.rate_limit_period}.",
        details: {
          limit: @api_playground.rate_limit_count,
          period: @api_playground.rate_limit_period,
          current_count: request_count,
          retry_after: remaining_time
        }
      }, status: :too_many_requests
    end
  end

  def parse_period(period_string)
    case period_string.to_s.downcase
    when "minute"
      1.minute
    when "hour"
      1.hour
    when "day"
      1.day
    else
      nil
    end
  end

  def calculate_remaining_time(period_duration)
    oldest_request = @api_playground.proxy_request_logs
                                   .where("requested_at >= ?", Time.current - period_duration)
                                   .order(:requested_at)
                                   .first

    return 0 unless oldest_request

    reset_time = oldest_request.requested_at + period_duration
    remaining_seconds = (reset_time - Time.current).to_i
    [ remaining_seconds, 0 ].max
  end

  def make_request(url, method = "GET", custom_headers = {}, body = nil, params = {})
    headers = {
      "User-Agent" => "FeedMob Proxy/1.0",
      "Accept" => "application/json, text/html, */*"
    }.merge(custom_headers)

    options = { headers: headers }
    http = HTTPX.with(options)

    case method.upcase
    when "GET"
      http.get(url, params: params.to_h)
    when "POST"
      http.post(url, body: body)
    else
      OpenStruct.new(error: "Unsupported HTTP method: #{method}", status: 400, headers: {}, body: "")
    end
  end

  def determine_error_type(response)
    return "rate_limit" if response.status == 429
    return "validation" if response.status.between?(400, 499)
    return "network" if response.status.between?(500, 599)
    return "timeout" if response.error&.include?("timeout")
    "unknown"
  end

  def format_error_message(response)
    case response.status
    when 400
      "Bad Request: Please check your request parameters."
    when 401
      "Unauthorized: Authentication required."
    when 403
      "Forbidden: Access denied to the requested resource."
    when 404
      "Not Found: The requested resource could not be found."
    when 429
      "Rate limit exceeded. Please try again later."
    when 500
      "Internal Server Error: The target server encountered an error."
    when 502
      "Bad Gateway: Invalid response from the target server."
    when 503
      "Service Unavailable: The target server is temporarily unavailable."
    when 504
      "Gateway Timeout: The target server did not respond in time."
    else
      response.error || "An unexpected error occurred."
    end
  end
end
