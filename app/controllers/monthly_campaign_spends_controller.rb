class MonthlyCampaignSpendsController < ApplicationController
  def index
    ahoy.track "Campaign Monthly Spends View", {
      query_params: campaign_spends_query_params
    }

    task = TaskDefinition.find_by(name: "verify_data/all_spend_job")
    @latest_task_run = if task
      task.task_runs.order(id: :desc).first
    end

    refresh_task = TaskDefinition.find_by(name: "refresh_spends_view_job")
    @latest_refresh_task_run = if refresh_task
      refresh_task.task_runs.order(id: :desc).first
    end

    @query = CampaignMonthlySpendsQuery.new(campaign_spends_query_params)
    @pagy, @campaign_spends = pagy(@query.results, limit: @query.limit)
  end

  def stats
    @query = CampaignMonthlySpendsQuery.new(campaign_spends_query_params)

    @partner_spend = @query.results.sum(:adjusted_net_spend)
    @client_spend = @query.results.sum(:adjusted_gross_spend)
    @revenue = @client_spend - @partner_spend
    @adjusted_net_spend = @query.results.sum(:net_spend_adjustment)
    @monthly_gross_adjustment = @query.results.sum(:monthly_gross_adjustment)
    @monthly_net_adjustment = @query.results.sum(:monthly_net_adjustment)
    @margin = (@client_spend - @partner_spend) / @client_spend.to_f
    @start_month = Date.parse("#{@query.start_month}-01")
    @end_month = Date.parse("#{@query.end_month}-01")

    render partial: "stats"
  end

  def trend
    @query = CampaignMonthlySpendsQuery.new(campaign_spends_query_params)
    @trend = @query.trend_data
    @trend = @trend + @query.year_trend_data

    render partial: "trend"
  end

  private

  def campaign_spends_query_params
    cleaned_params = params.fetch(:query, {}).permit(
      :start_month,
      :end_month,
      :sort_by,
      :limit,
      :hide_test_data,
      legacy_client_id_in: [],
      legacy_partner_id_in: [],
      legacy_campaign_id_in: []
    ).to_h

    # Clean up the params by:
    # 1. Removing keys with nil or empty string values
    # 2. Cleaning arrays by removing nil, empty strings, and blank values
    # 3. Removing array parameters if they become empty after cleaning
    cleaned_params.delete_if do |key, value|
      if value.is_a?(Array)
        # Clean the array
        value.reject!(&:blank?)
        # Remove the key if array is empty after cleaning
        value.empty?
      else
        # Remove if value is nil or empty string
        value.blank?
      end
    end

    cleaned_params
  end
end
