{"sizes": [0.3, 0.7], "master": {"widgets": ["FILTERS"], "sizes": [1]}, "detail": {"main": {"type": "split-area", "children": [{"type": "tab-area", "widgets": ["GRID_VIEW"], "currentIndex": 0}, {"type": "tab-area", "widgets": ["DAILY_SPEND"], "currentIndex": 0}], "orientation": "vertical", "sizes": [0.5, 0.5]}}, "mode": "globalFilters", "viewers": {"FILTERS": {"plugin": "Datagrid", "plugin_config": {"columns": {}, "edit_mode": "SELECT_ROW", "scroll_lock": false}, "columns_config": {}, "title": "<PERSON><PERSON><PERSON>", "group_by": ["Vendor Name", "Client Name", "Campaign Name"], "split_by": [], "sort": [["Vendor Name", "asc"]], "filter": [["Date", ">=", "<%= start_date %>"], ["Date", "<=", "<%= end_date %>"]], "columns": ["Gross Spend", "Net Spend", "Revenue"], "aggregates": {"Gross Spend": "sum", "Net Spend": "sum", "Revenue": "sum"}, "master": true, "table": "campaign_spends", "linked": false}, "GRID_VIEW": {"plugin": "Datagrid", "plugin_config": {"columns": {}, "edit_mode": "SELECT_ROW", "scroll_lock": false}, "columns_config": {}, "title": "Campaign Spends List", "group_by": [], "split_by": [], "sort": [["Date", "desc"], ["Client Name", "asc"]], "filter": [["Date", ">=", "<%= start_date %>"], ["Date", "<=", "<%= end_date %>"]], "expressions": {"Margin": "\"Revenue\" / \"Gross Spend\""}, "columns": ["Date", "Campaign Name", "Vendor Name", "Click", "Install", "Gross Spend", "Net Spend", "Revenue", "<PERSON><PERSON>"], "master": false, "aggregates": {}, "table": "campaign_spends", "settings": false, "linked": false}, "DAILY_SPEND": {"plugin": "Y Line", "plugin_config": {}, "columns_config": {}, "title": "Daily Spend", "group_by": ["Date"], "split_by": [], "sort": [["Date", "asc"]], "filter": [["Date", ">=", "<%= start_date %>"], ["Date", "<=", "<%= end_date %>"]], "expressions": {}, "columns": ["Gross Spend", "Net Spend"], "table": "campaign_spends", "aggregates": {}}}}