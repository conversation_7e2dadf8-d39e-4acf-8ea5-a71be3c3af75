class MobileAppCategory < ApplicationRecord
  has_many :mobile_apps, foreign_key: "main_category_id"
  has_many :mobile_app_categorizations, dependent: :destroy
  has_many :categorized_mobile_apps, through: :mobile_app_categorizations, source: :mobile_app

  validates :name, presence: true, uniqueness: true

  def self.find_or_create_by_name(name)
    return nil if name.blank?

    # Normalize the category name
    normalized_name = name.strip.titleize
    find_or_create_by(name: normalized_name)
  end
end
