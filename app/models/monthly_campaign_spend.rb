class MonthlyCampaignSpend < ApplicationRecord
  belongs_to :campaign, foreign_key: "legacy_campaign_id", primary_key: "legacy_id"
  belongs_to :partner, foreign_key: "legacy_partner_id", primary_key: "legacy_id"
  belongs_to :client, foreign_key: "legacy_client_id", primary_key: "legacy_id"

  belongs_to :monthly_campaign_spend_adjustment, optional: true

  def revenue
    adjusted_gross_spend - adjusted_net_spend
  end

  def self.refresh_data(date = Date.current.beginning_of_month.prev_month)
    data = CampaignMonthlySpendSummary.where("month_date >= ?", date).as_json
    upsert_all(data, unique_by: [ :month_date, :legacy_campaign_id, :legacy_partner_id, :legacy_client_id ])
  end
end
