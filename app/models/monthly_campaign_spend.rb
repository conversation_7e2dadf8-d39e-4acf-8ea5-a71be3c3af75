class MonthlyCampaignSpend < ApplicationRecord
  belongs_to :campaign, foreign_key: "legacy_campaign_id", primary_key: "legacy_id"
  belongs_to :partner, foreign_key: "legacy_partner_id", primary_key: "legacy_id"
  belongs_to :client, foreign_key: "legacy_client_id", primary_key: "legacy_id"

  belongs_to :monthly_campaign_spend_adjustment, optional: true

  def revenue
    adjusted_gross_spend - adjusted_net_spend
  end

  def self.refresh_data(date = Date.current.beginning_of_month.prev_month)
    data = CampaignMonthlySpendSummary.where("month_date >= ?", date).as_json
    return if data.empty?

    # Group by month for processing
    data_by_month = data.group_by { |d| d["month_date"] }

    transaction do
      data_by_month.each do |month_date, monthly_records|
        existing_keys = where(month_date: month_date)
                          .pluck(:legacy_campaign_id, :legacy_partner_id, :legacy_client_id)
                          .map { |campaign_id, partner_id, client_id| [ campaign_id, partner_id, client_id ] }

        new_keys = monthly_records.map { |d| [ d["legacy_campaign_id"], d["legacy_partner_id"], d["legacy_client_id"] ] }

        # Remove obsolete records for this month
        obsolete_keys = existing_keys - new_keys
        obsolete_keys.each do |campaign_id, partner_id, client_id|
          where(
            month_date: month_date,
            legacy_campaign_id: campaign_id,
            legacy_partner_id: partner_id,
            legacy_client_id: client_id
          ).delete_all
        end

        # Upsert new records for this month
        upsert_all(monthly_records, unique_by: [ :month_date, :legacy_campaign_id, :legacy_partner_id, :legacy_client_id ])
      end
    end
  end
end
