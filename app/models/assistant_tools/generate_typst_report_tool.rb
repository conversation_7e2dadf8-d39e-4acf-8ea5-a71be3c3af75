module AssistantTools
  class GenerateTypstReportTool < Base
    visible_to_users(false)

    def self.tool_spec
      {
        name: "generate_typst_report",
        description: "Generates a PDF report using Typst markup language. Creates a professional document with the provided content and structure.",
        input_schema: {
          json: {
            type: "object",
            required: [ "title", "content" ],
            properties: {
              title: {
                type: "string",
                description: "Report title"
              },
              author: {
                type: "string",
                description: "Report author (optional)"
              },
              content: {
                type: "string",
                description: "Main report content in Typst markup format"
              }
            }
          }
        }
      }
    end

    def call(context)
      typst_content = generate_typst_content
      pdf_path = compile_typst_to_pdf(typst_content)
      attach_pdf_to_message(context[:message], pdf_path)
      "PDF report generated and attached successfully"
    end

    private

    def generate_typst_content
      <<~TYPST
        #set text(font: "Liberation Serif", size: 11pt)
        #set page(margin: (x: 2cm, y: 2.5cm))
        #set par(justify: true, leading: 0.6em)
        #set heading(numbering: "1.")
        #show heading.where(level: 1): it => [
          #v(1em)
          #text(size: 16pt, weight: "bold", fill: rgb("#00B5AD"))[#it.body]
          #v(0.5em)
          #line(length: 100%, stroke: rgb("#00B5AD"))
          #v(0.5em)
        ]

        #set document(title: "#{input[:title]}", author: "#{input[:author] || 'FeedMob Assistant'}")
        #set page(numbering: "1")

        // FeedMob Logo
        #align(center)[
          #image("logo.png", width: 40%)
        ]

        #v(1em)

        #align(center)[
          #text(size: 24pt, weight: "bold")[#{input[:title]}]
        ]

        #{input[:author] ? "#align(center)[#text(size: 14pt)[#{input[:author]}]]" : ''}

        #v(1em)
        #line(length: 100%)
        #v(1em)

        #{input[:content]}
      TYPST
    end

    def compile_typst_to_pdf(typst_content)
      temp_dir = Dir.mktmpdir("typst_project")

      begin
        # Create files in the project directory
        temp_typst_file = File.join(temp_dir, "report.typ")
        temp_pdf_file = File.join(temp_dir, "report.pdf")

        # Copy assets to the temp directory
        copy_assets_to_temp_dir(temp_dir)

        # Write Typst content to file
        File.write(temp_typst_file, typst_content)

        # Compile Typst to PDF (no --root needed)
        command = [ "typst", "compile", temp_typst_file, temp_pdf_file ]

        # Capture both stdout and stderr
        _, stderr, status = Open3.capture3(*command)

        unless status.success? && File.exist?(temp_pdf_file)
          error_message = "Typst compilation failed"
          error_message += ": #{stderr.strip}" unless stderr.strip.empty?
          Rails.logger.error "#{error_message}. Command: #{command.join(' ')}"
          raise ToolExecError, error_message
        end

        Rails.logger.info "Successfully compiled Typst document to PDF"
        temp_pdf_file

      ensure
        # Don't clean up here - let attach_pdf_to_message handle it
      end
    end

    def copy_assets_to_temp_dir(temp_dir)
      assets_path = Rails.root.join("app", "assets", "images", "typst")
      return unless Dir.exist?(assets_path)

      Dir.glob(File.join(assets_path, "**", "*")).each do |file_path|
        next unless File.file?(file_path)

        relative_path = Pathname.new(file_path).relative_path_from(assets_path)
        dest_path = File.join(temp_dir, relative_path)

        FileUtils.mkdir_p(File.dirname(dest_path))
        FileUtils.cp(file_path, dest_path)
      end
    end

    def attach_pdf_to_message(message, pdf_path)
      begin
        File.open(pdf_path, "rb") do |file|
          message.files.attach(
            io: file,
            filename: "report-#{Time.current.to_i}.pdf",
            content_type: "application/pdf"
          )
        end

        Rails.logger.info "Successfully attached PDF report to message #{message.id}"
      ensure
        # Clean up the entire temp directory
        temp_dir = File.dirname(pdf_path)
        FileUtils.rm_rf(temp_dir) if Dir.exist?(temp_dir)
      end
    end
  end
end
