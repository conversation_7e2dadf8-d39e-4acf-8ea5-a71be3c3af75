class DailyCampaignSpend < ApplicationRecord
  belongs_to :click_url, foreign_key: "legacy_click_url_id", primary_key: "legacy_id"
  belongs_to :campaign, foreign_key: "legacy_campaign_id", primary_key: "legacy_id"
  belongs_to :partner, foreign_key: "legacy_partner_id", primary_key: "legacy_id"
  belongs_to :client, foreign_key: "legacy_client_id", primary_key: "legacy_id"

  belongs_to :gross_campaign_spend, class_name: "CampaignSpend", optional: true
  belongs_to :net_campaign_spend, class_name: "CampaignSpend", optional: true

  delegate :track_party, to: :click_url

  def revenue
    gross_spend - adjusted_net_spend
  end

  def self.refresh_data(date = Date.current.beginning_of_month.prev_month)
    data = CampaignSpendSummary.where("spend_date >= ?", date).map(&:attributes)
    upsert_all(data, unique_by: [ :spend_date, :legacy_click_url_id ])
  end
end
