class DailyCampaignSpend < ApplicationRecord
  belongs_to :click_url, foreign_key: "legacy_click_url_id", primary_key: "legacy_id"
  belongs_to :campaign, foreign_key: "legacy_campaign_id", primary_key: "legacy_id"
  belongs_to :partner, foreign_key: "legacy_partner_id", primary_key: "legacy_id"
  belongs_to :client, foreign_key: "legacy_client_id", primary_key: "legacy_id"

  belongs_to :gross_campaign_spend, class_name: "CampaignSpend", optional: true
  belongs_to :net_campaign_spend, class_name: "CampaignSpend", optional: true

  delegate :track_party, to: :click_url

  def revenue
    gross_spend - adjusted_net_spend
  end

  def self.refresh_data(date = Date.current.beginning_of_month.prev_month)
    data = CampaignSpendSummary.where("spend_date >= ?", date).map(&:attributes)

    # Get the date range for cleanup
    date_range = data.map { |d| d["spend_date"] }.compact.uniq

    transaction do
      # Remove existing records in the date range that are not in the new data
      if date_range.any?
        existing_keys = data.map { |d| [ d["spend_date"], d["legacy_click_url_id"] ] }
        where(spend_date: date_range).where.not(
          [ :spend_date, :legacy_click_url_id ] => existing_keys
        ).delete_all
      end

      # Upsert the new/updated data
      upsert_all(data, unique_by: [ :spend_date, :legacy_click_url_id ]) if data.any?
    end
  end
end
