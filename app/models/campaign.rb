class Campaign < ApplicationRecord
  belongs_to :client
  belongs_to :mobile_app, optional: true

  has_many :click_urls, dependent: :destroy
  has_many :monthly_campaign_spend_adjustments, dependent: :destroy
  has_many :budget_associations, as: :budgetable, dependent: :destroy
  has_many :client_budget_details, through: :budget_associations

  before_save :set_country_code

  private

  def set_country_code
    self.country_code = CountryCode.from_campaign_name(name)
  end
end
