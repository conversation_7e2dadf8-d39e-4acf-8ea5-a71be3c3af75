class ApiPlayground < ApplicationRecord
  audited

  belongs_to :client, optional: true
  belongs_to :partner, optional: true
  belongs_to :track_party, optional: true

  has_many :proxy_request_logs, dependent: :destroy

  validates :name, presence: true
  validates :slug, presence: true, uniqueness: true

  before_validation :generate_slug, if: -> { name.present? && slug.blank? }

  def to_param
    slug
  end

  private

  def generate_slug
    self.slug = name.parameterize
  end
end
