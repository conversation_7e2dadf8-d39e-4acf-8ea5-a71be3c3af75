<div class="w-full h-full flex flex-col bg-gray-50 dark:bg-gray-950">
  <div class="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
    <div class="mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between py-4">
        <div class="flex items-center space-x-4">
          <%= link_to client_path(@client_budget.client), class: "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" do %>
            <%= icon "arrow-left", class: "h-5 w-5" %>
          <% end %>
          <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
            Add Budget Detail for <%= @client_budget.client.name %>
          </h1>
        </div>
        <div class="flex items-center space-x-3">
          <%= link_to "Cancel", client_path(@client_budget.client), 
              class: "inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
          <button type="submit" form="new_client_budget_detail" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Create Budget Detail
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="grow overflow-y-auto">
    <div class="mx-auto  px-4 sm:px-6 lg:px-8 py-6">
      <div class="bg-white dark:bg-gray-900 shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">Budget Detail Information</h2>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Budget: $<%= number_with_delimiter(@client_budget.amount.to_i) %> 
            (<%= @client_budget.start_date.strftime("%b %d, %Y") %> - <%= @client_budget.end_date.strftime("%b %d, %Y") %>)
          </p>
        </div>

        <div class="p-6" data-controller="campaign-selector">
          <%= form_with model: [@client_budget.client, @client_budget, @client_budget_detail], local: true, class: "space-y-6", id: "new_client_budget_detail" do |form| %>

            <% if @client_budget_detail.errors.any? %>
              <div class="rounded-md bg-red-50 dark:bg-red-900/50 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <%= icon "exclamation-circle", class: "h-5 w-5 text-red-400" %>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                      There were <%= pluralize(@client_budget_detail.errors.count, "error") %> with your submission:
                    </h3>
                    <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                      <ul role="list" class="list-disc list-inside space-y-1">
                        <% @client_budget_detail.errors.full_messages.each do |message| %>
                          <li><%= message %></li>
                        <% end %>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>

            <div>
              <%= form.label :amount, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
              <div class="mt-1 relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span class="text-gray-500 dark:text-gray-400 sm:text-sm">$</span>
                </div>
                <%= form.number_field :amount, step: 0.01, min: 0, 
                    class: "block w-full pl-7 pr-12 border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white sm:text-sm",
                    placeholder: "0.00" %>
              </div>
            </div>

            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Select Countries (Multi-select)
                </label>
                <button type="button" data-action="click->campaign-selector#toggleAll" class="px-3 py-1 text-sm rounded-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                  Select All
                </button>
              </div>
              <div class="flex flex-wrap gap-2 mb-4">
                <% @countries.each do |country| %>
                  <button type="button" data-action="click->campaign-selector#toggleCountry" data-country="<%= country %>" class="px-3 py-1 text-sm rounded-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <%= country %>
                  </button>
                <% end %>
              </div>
            </div>

            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Campaigns
                </label>
     
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-2">
                   <% @campaigns.each do |campaign| %>
                     <label class="campaign-item flex items-center py-1 px-2 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer text-sm" data-country="<%= campaign.country_code %>" data-campaign-selector-target="campaignOption">
                       <input type="checkbox" name="campaign_ids[]" value="<%= campaign.id %>" 
                              class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-800 mr-2 flex-shrink-0">
                       <span class="text-gray-900 dark:text-white truncate flex-1"><%= campaign.name %></span>
                       <% if campaign.country_code.present? %>
                         <span class="text-xs text-gray-500 dark:text-gray-400 ml-1">(<%= campaign.country_code %>)</span>
                       <% end %>
                     </label>
                   <% end %>
                 </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>