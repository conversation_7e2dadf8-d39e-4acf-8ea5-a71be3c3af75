<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
  <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
    <div class="flex items-center justify-between">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "currency-dollar", class: "mr-2 h-4 w-4" %>
        Budgets
      </h3>
      <% if @client.client_budgets.any? %>
        <span class="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/50 px-2 py-0.5 text-xs font-medium text-green-700 dark:text-green-300">
          <%= @client.client_budgets.count %>
        </span>
      <% end %>
    </div>
  </div>
  <div class="px-2">
    <% if @client.client_budgets.any? %>
      <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-800">
        <% @client.client_budgets.order(start_date: :desc).limit(5).each do |budget| %>
          <li class="py-3 px-2">
            <div class="flex items-center justify-between">
              <div class="min-w-0 flex-1">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  $<%= number_with_delimiter(budget.amount.to_i) %>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  <%= budget.start_date.strftime("%b %d, %Y") %> - <%= budget.end_date.strftime("%b %d, %Y") %>
                </div>
              </div>
              <div class="flex-shrink-0">
                <% days_total = (budget.end_date - budget.start_date).to_i + 1 %>
                <% days_remaining = [(budget.end_date - Date.current).to_i + 1, 0].max %>
                <% if days_remaining > 0 %>
                  <span class="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/50 px-2 py-0.5 text-xs font-medium text-green-700 dark:text-green-300">
                    <%= days_remaining %>d left
                  </span>
                <% else %>
                  <span class="inline-flex items-center rounded-full bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-xs font-medium text-gray-700 dark:text-gray-300">
                    Ended
                  </span>
                <% end %>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    <% else %>
      <div class="text-center py-6">
        <p class="text-xs text-gray-500 dark:text-gray-400">This client has no budgets set</p>
      </div>
    <% end %>
  </div>
</div>
