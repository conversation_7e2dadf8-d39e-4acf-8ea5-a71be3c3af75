<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
  <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-800">
    <div class="flex items-center justify-between">
      <h3 class="text-base font-medium text-gray-900 dark:text-white flex items-center">
        <%= icon "currency-dollar", class: "mr-2 h-4 w-4" %>
        Budgets
      </h3>
      <% if @client.client_budgets.any? %>
        <span class="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/50 px-2 py-0.5 text-xs font-medium text-green-700 dark:text-green-300">
          <%= @client.client_budgets.count %>
        </span>
      <% end %>
    </div>
  </div>
  <div class="px-2">
    <% if @client.client_budgets.any? %>
      <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-800">
        <% @client.client_budgets.order(start_date: :desc).limit(5).each do |budget| %>
          <li class="py-3 px-2 relative">
            <div class="flex items-center justify-between">
                <div class="min-w-0 flex-1">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    $<%= number_with_delimiter(budget.amount.to_i) %>
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                    <span><%= budget.start_date.strftime("%b %d, %Y") %> - <%= budget.end_date.strftime("%b %d, %Y") %></span>
                    <% if budget.client_budget_details.any? %>
                      <span class="ml-2 flex items-center space-x-1">
                        <button type="button" 
                                class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-500 focus:outline-none"
                                data-controller="campaign-selector"
                                data-action="click->campaign-selector#toggleBudgetDetails"
                                data-target="budget-<%= budget.id %>-details">
                          <%= pluralize(budget.client_budget_details.count, 'detail') %> ▼
                        </button>
                        <span class="text-gray-300 dark:text-gray-600">|</span>
                        <%= link_to "Add", new_client_client_budget_client_budget_detail_path(@client, budget), 
                            class: "text-xs text-blue-600 dark:text-blue-400 hover:text-blue-500" %>
                      </span>
                    <% else %>
                      <span class="ml-2">
                        <%= link_to "Add", new_client_client_budget_client_budget_detail_path(@client, budget), 
                            class: "text-xs text-blue-600 dark:text-blue-400 hover:text-blue-500" %>
                      </span>
                    <% end %>
                  </div>
                </div>
                <div class="flex-shrink-0 flex items-center">
                <% days_total = (budget.end_date - budget.start_date).to_i + 1 %>
                <% days_remaining = [(budget.end_date - Date.current).to_i + 1, 0].max %>
                <% if days_remaining > 0 %>
                  <span class="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/50 px-2 py-0.5 text-xs font-medium text-green-700 dark:text-green-300">
                    <%= days_remaining %>d left
                  </span>
                <% else %>
                  <span class="inline-flex items-center rounded-full bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-xs font-medium text-gray-700 dark:text-gray-300">
                    Ended
                   </span>
                 <% end %>
               </div>
             </div>
             <% if budget.client_budget_details.any? %>
               <div id="budget-<%= budget.id %>-details" class="hidden mt-1 bg-gray-50 dark:bg-gray-800 rounded p-1 border border-gray-200 dark:border-gray-700">
                 <div class="space-y-0.5">
                   <% budget.client_budget_details.each do |detail| %>
                     <div class="py-0.5 px-1 bg-white dark:bg-gray-900 rounded border border-gray-200 dark:border-gray-700">
                       <div class="flex items-center justify-between">
                         <span class="text-gray-900 dark:text-white text-xs">
                           $<%= number_with_delimiter(detail.amount.to_i) %>
                         </span>
                         <div class="flex items-center space-x-0.5">
                           <%= link_to "Edit", 
                               edit_client_client_budget_client_budget_detail_path(@client, budget, detail), 
                               class: "text-blue-600 dark:text-blue-400 hover:text-blue-500 text-xs" %>
                           <span class="text-gray-300 dark:text-gray-600 text-xs">|</span>
                           <%= link_to "Delete", 
                               client_client_budget_client_budget_detail_path(@client, budget, detail), 
                               data: { turbo_method: :delete, turbo_confirm: "Are you sure you want to delete this budget detail?" },
                               class: "text-red-600 dark:text-red-400 hover:text-red-500 text-xs" %>
                         </div>
                       </div>
                       <% if detail.campaigns.any? %>
                         <div class="text-gray-600 dark:text-gray-300 mt-0.5">
                           <div class="text-xs mb-0.5">Campaigns (<%= detail.campaigns.count %>):</div>
                           <div class="space-y-0.5">
                             <% detail.campaigns.each do |campaign| %>
                               <div class="flex items-center justify-between py-0.5 px-1 bg-gray-50 dark:bg-gray-800 rounded text-xs">
                                 <span class="text-gray-800 dark:text-gray-200"><%= campaign.name %></span>
                                 <% if campaign.country_code.present? %>
                                   <span class="text-gray-500 dark:text-gray-400">(<%= campaign.country_code %>)</span>
                                 <% end %>
                               </div>
                             <% end %>
                           </div>
                         </div>
                       <% else %>
                         <div class="text-gray-500 dark:text-gray-400 italic text-xs mt-0.5">
                           No campaigns assigned
                         </div>
                       <% end %>
                     </div>
                   <% end %>
                 </div>
               </div>
             <% end %>
           </li>
        <% end %>
      </ul>
    <% else %>
      <div class="text-center py-6">
        <p class="text-xs text-gray-500 dark:text-gray-400">This client has no budgets set</p>
      </div>
    <% end %>
  </div>
</div>
