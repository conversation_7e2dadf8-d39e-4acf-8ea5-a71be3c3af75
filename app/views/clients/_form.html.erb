<%= form_with(model: client, local: true, html: { multipart: true }) do |form| %>
  <div class="space-y-6">
    <!-- Basic Information -->
    <%= render "clients/form_sections/basic_information", form: form, client: client %>

    <!-- Client Documents -->
    <%= render "clients/form_sections/client_documents", form: form, client: client %>

    <!-- Client Creatives -->
    <%= render "clients/form_sections/client_creatives", form: form, client: client %>

    <!-- Client Details -->
    <%= render "clients/form_sections/client_details", form: form, client: client %>

    <!-- Client Direct Partners -->
    <%= render "clients/form_sections/client_direct_partners", form: form %>

    <!-- Client Track Parties -->
    <%= render "clients/form_sections/client_track_parties", form: form %>

    <!-- Client Events -->
    <%= render "clients/form_sections/client_events", form: form, client: client %>

    <!-- Client Reports -->
    <%= render "clients/form_sections/client_reports", form: form, client: client %>

    <!-- Client Interests -->
    <%= render "clients/form_sections/client_interests", form: form, client: client %>

    <!-- Client Social Media Links -->
    <%= render "clients/form_sections/client_social_media_links", form: form, client: client %>

    <!-- Client Fraud Platforms -->
    <%= render "clients/form_sections/client_fraud_platforms", form: form, client: client %>

    <!-- Deal Scores -->
    <%= render "clients/form_sections/deal_scores", form: form, client: client %>

    <!-- Form Actions -->
    <div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm sticky bottom-0 z-10">
      <div class="px-6 py-5 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900">
        <div class="flex items-center justify-between">
          <!-- Left side - Form status/info -->
          <div class="flex items-center space-x-4">
            <% if client.persisted? %>
              <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <%= icon "clock", class: "mr-1.5 h-4 w-4" %>
                Last updated <%= time_ago_in_words(client.updated_at) %> ago
              </div>
            <% else %>
              <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <%= icon "plus-circle", class: "mr-1.5 h-4 w-4" %>
                Creating new client
              </div>
            <% end %>
          </div>

          <!-- Right side - Action buttons -->
          <div class="flex items-center space-x-3">
            <%= link_to client.persisted? ? client_path(client) : clients_path, 
                        class: "inline-flex items-center px-5 py-2.5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out" do %>
              <%= icon "x-mark", class: "mr-2 h-4 w-4" %>
              Cancel
            <% end %>
            
            <% if client.persisted? %>
              <%= form.submit "Update Client", 
                              class: "cursor-pointer inline-flex items-center px-6 py-2.5 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98]",
                              data: { disable_with: "#{icon('arrow-path', class: 'animate-spin mr-2 h-4 w-4')} Updating...".html_safe } %>
            <% else %>
              <%= form.submit "Create Client", 
                              class: "cursor-pointer inline-flex items-center px-6 py-2.5 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98]",
                              data: { disable_with: "#{icon('arrow-path', class: 'animate-spin mr-2 h-4 w-4')} Creating...".html_safe } %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
