<div class="bg-gray-900 overflow-hidden rounded-xl border border-gray-800 shadow-sm">
  <div class="px-6 py-5 border-b border-gray-800">
    <h3 class="text-base font-semibold leading-6 text-white">Client Details</h3>
    <p class="mt-1 max-w-2xl text-sm text-gray-400">
      Additional information about the client and their requirements.
    </p>
  </div>
  
  <%= form.fields_for :client_detail, (client.client_detail || client.build_client_detail) do |detail_form| %>
    <div class="px-6 py-6 space-y-6">
      <!-- Contact Name -->
      <div>
        <%= detail_form.label :contact_name, class: "block text-sm font-medium text-gray-300 mb-2" %>
        <%= detail_form.text_field :contact_name, 
                                   class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm",
                                   placeholder: "Primary contact name" %>
      </div>

      <!-- Account Executive -->
      <div>
        <%= detail_form.label :account_executive_user_id, "Account Executive", class: "block text-sm font-medium text-gray-300 mb-2" %>
        <%= detail_form.select :account_executive_user_id, 
                               options_from_collection_for_select(User.all, :id, :name, client.client_detail&.account_executive_user_id),
                               { prompt: "Select Account Executive" },
                               class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" %>
      </div>

      <!-- Account Manager -->
      <div>
        <%= detail_form.label :account_manager_user_id, "Account Manager", class: "block text-sm font-medium text-gray-300 mb-2" %>
        <%= detail_form.select :account_manager_user_id, 
                               options_from_collection_for_select(User.all, :id, :name, client.client_detail&.account_manager_user_id),
                               { prompt: "Select Account Manager" },
                               class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" %>
      </div>

      <!-- Preferred Communication Channel -->
      <div>
        <%= detail_form.label :preferred_communication_channel, class: "block text-sm font-medium text-gray-300 mb-2" %>
        <%= detail_form.select :preferred_communication_channel,
                               options_for_select([
                                 ['Email', 'email'],
                                 ['Slack', 'slack'],
                                 ['Phone', 'phone'],
                                 ['Teams', 'teams'],
                                 ['Other', 'other']
                               ], client.client_detail&.preferred_communication_channel),
                               { prompt: "Select preferred channel" },
                               class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" %>
      </div>

      <!-- Supported Geos -->
      <div>
        <%= detail_form.label :supported_geos, class: "block text-sm font-medium text-gray-300 mb-3" %>
        
        <!-- Scrollable container with max height -->
        <div class="max-h-64 overflow-y-auto border border-gray-600 rounded-lg p-3 bg-gray-800/50">
          <!-- Grid layout for compact display -->
          <div class="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-x-4 gap-y-1">
            <% CountryCode::CODES.map {|code| [code, code]}.each do |label, value| %>
              <div class="flex items-center py-1">
                <%= detail_form.check_box :supported_geos, 
                                          { multiple: true, 
                                            checked: client.client_detail&.supported_geos&.include?(value),
                                            class: "h-4 w-4 rounded border-gray-600 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 bg-gray-700 focus:ring-2 focus:ring-offset-0" },
                                          value, nil %>
                <%= detail_form.label "supported_geos_#{value}", label, 
                                      class: "ml-2 text-sm text-gray-300 cursor-pointer select-none truncate" %>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Client Persona -->
      <div>
        <%= detail_form.label :client_persona, class: "block text-sm font-medium text-gray-300 mb-2" %>
        <%= detail_form.text_area :client_persona, 
                                  rows: 3,
                                  class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm",
                                  placeholder: "Describe the client's personality and working style..." %>
      </div>

      <!-- Target Audience -->
      <div>
        <%= detail_form.label :target_audience, class: "block text-sm font-medium text-gray-300 mb-2" %>
        <%= detail_form.text_area :target_audience, 
                                  rows: 3,
                                  class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm",
                                  placeholder: "Describe the client's target audience..." %>
      </div>

      <!-- User Behavior Notes -->
      <div>
        <%= detail_form.label :user_behavior_notes, class: "block text-sm font-medium text-gray-300 mb-2" %>
        <%= detail_form.text_area :user_behavior_notes, 
                                  rows: 3,
                                  class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm",
                                  placeholder: "Notes about user behavior patterns..." %>
      </div>

      <!-- Biggest Obstacles -->
      <div>
        <%= detail_form.label :biggest_obstacles, class: "block text-sm font-medium text-gray-300 mb-2" %>
        <%= detail_form.text_area :biggest_obstacles, 
                                  rows: 3,
                                  class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm",
                                  placeholder: "What are the biggest challenges or obstacles?" %>
      </div>

      <!-- Noteworthy Restrictions -->
      <div>
        <%= detail_form.label :noteworthy_restrictions, class: "block text-sm font-medium text-gray-300 mb-2" %>
        <%= detail_form.text_area :noteworthy_restrictions, 
                                  rows: 3,
                                  class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm",
                                  placeholder: "Any important restrictions or limitations..." %>
      </div>

      <div>
        <%= detail_form.label :user_flow_description, class: "block text-sm font-medium text-gray-300 mb-2" %>
        <%= detail_form.text_area :user_flow_description, 
                                  rows: 3,
                                  class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm",
                                  placeholder: "Describe the user flow and experience..." %>
      </div>

      <!-- Additional Notes -->
      <div>
        <%= detail_form.label :additional_notes, class: "block text-sm font-medium text-gray-300 mb-2" %>
        <%= detail_form.text_area :additional_notes, 
                                  rows: 4,
                                  class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm",
                                  placeholder: "Any additional notes or important information..." %>
      </div>
    </div>
  <% end %>
</div>
