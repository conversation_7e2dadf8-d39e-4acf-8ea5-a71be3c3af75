<div class="bg-gray-950 py-4">
  <div class="mx-auto max-w-8xl px-4 sm:px-6 2xl:px-12">
    <section aria-labelledby="filter-heading" data-controller="url-cleaner" data-url-cleaner-filter-export-value="true">
      <%= form_with model: @query, scope: :query, url: campaign_spends_path, method: :get, data: { controller: 'auto-submit' }, html: { id: 'filter-form' } do |form| %>
        <div class="flex items-center justify-between">
          <!-- Left side filters -->
          <div class="flex items-center space-x-3">
            <!-- Sort Dropdown -->
            <div data-controller="dropdown" class="relative inline-block">
              <button type="button"
                      data-action="dropdown#toggle"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-gray-100 bg-gray-800 rounded-md hover:bg-gray-700 transition-colors duration-150">
                Sort
                <%= icon "chevron-down", set: "mini", class: "-mr-1 ml-1.5 h-5 w-5 text-gray-400" %>
              </button>

              <div data-dropdown-target="menu"
                  data-transition-enter-from="opacity-0 scale-95"
                  data-transition-enter-to="opacity-100 scale-100"
                  data-transition-leave-from="opacity-100 scale-100"
                  data-transition-leave-to="opacity-0 scale-95"
                  class="hidden absolute left-0 z-20 mt-2 min-w-[160px] origin-top-left rounded-md bg-gray-800 shadow-lg ring-1 ring-gray-700 focus:outline-none">
                <div class="py-1">
                  <%= form.collection_radio_buttons :sort_by, [
                    ['date_asc', 'Oldest'],
                    ['date_desc', 'Newest'],
                    ['gross_spend_asc', 'Lowest Gross Spend'],
                    ['gross_spend_desc', 'Highest Gross Spend'],
                    ['net_spend_asc', 'Lowest Net Spend'],
                    ['net_spend_desc', 'Highest Net Spend'],
                    ['margin_asc', 'Lowest Margin'],
                    ['margin_desc', 'Highest Margin'],
                    ['revenue_asc', 'Lowest Revenue'],
                    ['revenue_desc', 'Highest Revenue']
                  ],
                      :first, :last, {}, data: { action: "auto-submit#submit" } do |b| %>
                    <label class="flex items-center px-4 py-2 hover:bg-gray-700 cursor-pointer">
                      <%= b.radio_button class: "hidden peer" %>
                      <span class="text-sm font-medium text-gray-200 peer-checked:text-primary-400"><%= b.text %></span>
                      <%= icon "check", class: "ml-auto h-5 w-5 text-primary-400 hidden peer-checked:block" %>
                    </label>
                  <% end %>
                </div>
              </div>
            </div>


            <!-- Limit Dropdown -->
            <div data-controller="dropdown" class="relative">
              <button type="button"
                      data-action="click->dropdown#toggle"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-gray-100 bg-gray-800 rounded-md hover:bg-gray-700 transition-colors duration-150">
                Limit
                <%= icon "chevron-down", set: "mini", class: "-mr-1 ml-1.5 h-5 w-5 text-gray-400" %>
              </button>

              <div data-dropdown-target="menu"
                  class="hidden absolute right-0 z-20 mt-2 w-40 origin-top-right rounded-md bg-gray-800 shadow-lg ring-1 ring-gray-700 focus:outline-none">
                <div class="py-1">
                  <%= form.collection_radio_buttons :limit, [[20, '20'], [100, '100']],
                      :first, :last, {}, data: { action: "auto-submit#submit" } do |b| %>
                    <label class="flex items-center px-4 py-2 hover:bg-gray-700 cursor-pointer">
                      <%= b.radio_button class: "hidden peer" %>
                      <span class="text-sm font-medium text-gray-200 peer-checked:text-primary-400"><%= b.text %></span>
                      <%= icon "check", class: "ml-auto h-5 w-5 text-primary-400 hidden peer-checked:block" %>
                    </label>
                  <% end %>
                </div>
              </div>
            </div>
            <div class="ml-3">
              <%= render 'campaign_spends/export_report', form: form %>
            </div>
          </div>

          <!-- Right side filters -->
          <div class="flex items-center space-x-3">
            <!-- Hide Test Data Checkbox -->
            <div class="flex items-center">
              <label class="flex items-center space-x-2 cursor-pointer">
                <%= form.check_box :hide_test_data,
                    class: "h-4 w-4 rounded border-gray-600 bg-gray-700 text-primary-600 focus:ring-primary-400",
                    data: { action: "auto-submit#submit" } %>
                <span class="text-sm font-medium text-gray-300">
                  Hide Test
                </span>
              </label>
            </div>

            <!-- Platform Dropdown -->
            <div data-controller="dropdown" class="relative">
              <button type="button"
                      data-action="click->dropdown#toggle"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-gray-100 bg-gray-800 rounded-md hover:bg-gray-700 transition-colors duration-150">
                Platform
                <%= icon "chevron-down", set: "mini", class: "-mr-1 ml-1.5 h-5 w-5 text-gray-400" %>
              </button>

              <div data-dropdown-target="menu"
                  class="hidden absolute right-0 z-20 mt-2 w-40 origin-top-right rounded-md bg-gray-800 shadow-lg ring-1 ring-gray-700 focus:outline-none">
                <div class="py-1">
                  <%= form.collection_radio_buttons :platform, [["", 'All Platforms'], ["Android", 'Android'], ["iOS", 'iOS'], ["Universal", 'Universal'], ["mweb", 'Mobile Web']],
                      :first, :last, {}, data: { action: "auto-submit#submit" } do |b| %>
                    <label class="flex items-center px-4 py-2 hover:bg-gray-700 cursor-pointer">
                      <%= b.radio_button class: "hidden peer" %>
                      <span class="text-sm font-medium text-gray-200 peer-checked:text-primary-400"><%= b.text %></span>
                      <%= icon "check", class: "ml-auto h-5 w-5 text-primary-400 hidden peer-checked:block" %>
                    </label>
                  <% end %>
                </div>
              </div>
            </div>

            <!-- Client Filter Dropdown -->
            <%= filter_dropdown(
              form: form,
              field: :legacy_client_id_in,
              collection: @query.clients,
              label: "Client",
              selected: @query.legacy_client_id_in,
              options: {
                value_method: :legacy_id,
                label_method: :name
              }
            ) %>

            <!-- Vendor Filter Dropdown -->
            <%= filter_dropdown(
              form: form,
              field: :legacy_partner_id_in,
              collection: @query.partners,
              label: "Vendor",
              selected: @query.legacy_partner_id_in,
              options: {
                value_method: :legacy_id,
                label_method: :name
              }
            ) %>

            <!-- Campaign Filter Dropdown -->
            <%= filter_dropdown(
              form: form,
              field: :legacy_campaign_id_in,
              collection: @query.campaigns,
              label: "Campaign",
              selected: @query.legacy_campaign_id_in,
              options: {
                value_method: :legacy_id,
                label_method: :name
              }
            ) %>

            <!-- Click URL ID Filter Dropdown -->
            <%= filter_dropdown(
              form: form,
              field: :legacy_click_url_id_in,
              collection: @query.legacy_click_url_ids,
              label: "Click URL ID",
              selected: @query.legacy_click_url_id_in,
              options: {
                value_method: :legacy_id,
                label_method: :legacy_id
              }
            ) %>

            <!-- Date Range -->
            <div data-controller="dropdown" class="relative">
              <button type="button"
                      data-action="dropdown#toggle"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-gray-100 bg-gray-800 rounded-md hover:bg-gray-700 transition-colors duration-150">
                Date
                <%= icon "calendar", set: "mini", class: "-mr-1 ml-1.5 h-5 w-5 text-gray-400" %>
              </button>

              <div data-dropdown-target="menu"
                  data-controller="date-range"
                  class="hidden absolute right-0 z-20 mt-2 w-64 origin-top-right rounded-md bg-gray-800 p-4 shadow-lg ring-1 ring-gray-700">
                <div class="space-y-3">
                  <div>
                    <%= form.label :date_gteq, "Start Date", class: "block text-sm font-medium text-gray-200" %>
                    <%= form.date_field :date_gteq,
                        data: { date_range_target: "startDate" },
                        class: "mt-1 block w-full rounded-md border-0 bg-gray-700 px-3 py-2 text-gray-100 ring-1 ring-inset ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm" %>
                  </div>
                  <div>
                    <%= form.label :date_lteq, "End Date", class: "block text-sm font-medium text-gray-200" %>
                    <%= form.date_field :date_lteq,
                        data: { date_range_target: "endDate" },
                        class: "mt-1 block w-full rounded-md border-0 bg-gray-700 px-3 py-2 text-gray-100 ring-1 ring-inset ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm" %>
                  </div>

                  <%= render "campaign_spends/quick_select_buttons", form: form %>
                </div>
              </div>
            </div>

          </div>
        </div>
        <%= form.submit "filter", class: "hidden", id: "filter-submit" %>
      <% end %>
    </section>
  </div>
</div>

