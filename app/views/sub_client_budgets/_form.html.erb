<%= form_with(model: [@client_budget, sub_client_budget], local: true) do |f| %>
  <% if sub_client_budget.errors.any? %>
    <div class="error-messages">
      <h2><%= pluralize(sub_client_budget.errors.count, "error") %> prohibited this sub-budget from being saved:</h2>
      <ul>
        <% sub_client_budget.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-group">
    <%= f.label :name %>
    <%= f.text_field :name, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :amount %>
    <%= f.number_field :amount, step: '0.01', class: 'form-control' %>
  </div>

  <div class="form-group">
    <h3>Associated Campaigns</h3>
    <%= f.fields_for :sub_client_budget_campaigns do |campaign_form| %>
      <div class="nested-fields">
        <%= campaign_form.collection_select :campaign_id, Campaign.all, :id, :name, {}, class: 'form-control' %>
        <%= campaign_form.check_box :_destroy %>
        <%= campaign_form.label :_destroy, 'Remove' %>
      </div>
    <% end %>
    <%= link_to_add_association 'Add Campaign', f, :sub_client_budget_campaigns, class: 'btn btn-secondary' %>
  </div>

  <div class="actions">
    <%= f.submit class: 'btn btn-primary' %>
    <%= link_to 'Back', client_budget_path(@client_budget), class: 'btn btn-secondary' %>
  </div>
<% end %>