<% # filepath: /Users/<USER>/Projects/assistant/app/views/api_playgrounds/index.html.erb %>
<% content_for :title, "API Playgrounds" %>

<div class="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-gray-950 p-6">
  <div class="max-w-7xl mx-auto">
    <!-- Header Section -->
    <div class="relative overflow-hidden rounded-2xl bg-gradient-to-r from-gray-800/80 to-gray-700/80 p-8 mb-8 backdrop-blur-sm border border-gray-700/50">
      <div class="absolute inset-0 bg-gradient-to-r from-primary-600/10 to-transparent"></div>
      <div class="relative sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center mb-3">
            <%= icon "zap", library: "lucide", class: "h-8 w-8 text-primary-400 mr-3" %>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              API Playgrounds
            </h1>
          </div>
          <p class="text-gray-400 text-lg leading-relaxed">
            Interactive environment for managing and testing various API configurations
          </p>
          <div class="flex items-center mt-4 space-x-6 text-sm text-gray-500">
            <span class="flex items-center">
              <%= icon "database", library: "lucide", class: "h-4 w-4 mr-1" %>
              <%= pluralize(@api_playgrounds.count, "playground") %>
            </span>
            <span class="flex items-center">
              <%= icon "clock", library: "lucide", class: "h-4 w-4 mr-1" %>
              Last updated <%= time_ago_in_words(DateTime.current) %> ago
            </span>
          </div>
        </div>
        <div class="mt-6 sm:mt-0 sm:ml-16 sm:flex-none">
          <%= link_to new_api_playground_path, class: "group relative inline-flex items-center overflow-hidden rounded-xl bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-3 text-sm font-semibold text-white shadow-lg transition-all duration-300 hover:from-primary-500 hover:to-primary-600 hover:shadow-xl hover:shadow-primary-500/25 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900" do %>
            <div class="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
            <%= icon "plus", library: "lucide", class: "h-4 w-4 mr-2 transition-transform group-hover:scale-110" %>
            New Playground
          <% end %>
        </div>
      </div>
    </div>

    <!-- Content Section -->
    <div class="space-y-6">
      <% if @api_playgrounds.any? %>
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="bg-gradient-to-br from-blue-900/30 to-blue-800/20 rounded-xl p-6 border border-blue-700/30">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-blue-300 text-sm font-medium">Client APIs</p>
                <p class="text-2xl font-bold text-white"><%= @api_playgrounds.joins(:client).count %></p>
              </div>
              <%= icon "heart-handshake", library: "lucide", class: "h-8 w-8 text-blue-400" %>
            </div>
          </div>
          <div class="bg-gradient-to-br from-green-900/30 to-green-800/20 rounded-xl p-6 border border-green-700/30">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-green-300 text-sm font-medium">Partner APIs</p>
                <p class="text-2xl font-bold text-white"><%= @api_playgrounds.joins(:partner).count %></p>
              </div>
              <%= icon "package", library: "lucide", class: "h-8 w-8 text-green-400" %>
            </div>
          </div>
          <div class="bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-xl p-6 border border-purple-700/30">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-purple-300 text-sm font-medium">Track Party APIs</p>
                <p class="text-2xl font-bold text-white"><%= @api_playgrounds.joins(:track_party).count %></p>
              </div>
              <%= icon "target", library: "lucide", class: "h-8 w-8 text-purple-400" %>
            </div>
          </div>
          <div class="bg-gradient-to-br from-gray-900/30 to-gray-800/20 rounded-xl p-6 border border-gray-700/30">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-300 text-sm font-medium">Unassigned</p>
                <p class="text-2xl font-bold text-white"><%= @api_playgrounds.where(client: nil, partner: nil, track_party: nil).count %></p>
              </div>
              <%= icon "circle-minus", library: "lucide", class: "h-8 w-8 text-gray-400" %>
            </div>
          </div>
        </div>

        <!-- Main Table -->
        <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-700/50 overflow-hidden shadow-2xl">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-700/50">
              <thead class="bg-gradient-to-r from-gray-800/80 to-gray-700/80">
                <tr>
                  <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                      <%= icon "layers", library: "lucide", class: "h-4 w-4" %>
                      <span>Playground</span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                      <%= icon "file-text", library: "lucide", class: "h-4 w-4" %>
                      <span>Description</span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-300 uppercase tracking-wider">
                    <div class="flex items-center space-x-1">
                      <%= icon "link", library: "lucide", class: "h-4 w-4" %>
                      <span>Association</span>
                    </div>
                  </th>
                  <th scope="col" class="relative px-6 py-4 w-32">
                    <span class="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody class="bg-gray-800/30 divide-y divide-gray-700/30">
                <% @api_playgrounds.each_with_index do |playground, index| %>
                  <tr class="group hover:bg-gradient-to-r hover:from-gray-700/40 hover:to-gray-600/40">
                    <td class="px-6 py-4">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mr-4">
                          <%= icon "zap", library: "lucide", class: "h-5 w-5 text-white" %>
                        </div>
                        <div>
                          <div class="text-sm font-semibold text-white group-hover:text-primary-300 transition-colors duration-200">
                            <%= link_to playground.name, playground, class: "hover:text-primary-400 transition-colors duration-200" %>
                          </div>
                          <div class="text-xs text-gray-400 mt-1 font-mono bg-gray-900/50 px-2 py-0.5 rounded">
                            <%= playground.slug %>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <div class="text-sm text-gray-300 max-w-xs leading-relaxed">
                        <%= playground.description.present? ? truncate(playground.description, length: 100) : "No description provided" %>
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <% if playground.client %>
                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-900/60 to-blue-800/60 text-blue-200 border border-blue-600/30 shadow-sm">
                          <%= icon "heart-handshake", library: "lucide", class: "h-3 w-3 mr-1.5" %>
                          <%= playground.client.name %>
                        </span>
                      <% elsif playground.partner %>
                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-green-900/60 to-green-800/60 text-green-200 border border-green-600/30 shadow-sm">
                          <%= icon "package", library: "lucide", class: "h-3 w-3 mr-1.5" %>
                          <%= playground.partner.name %>
                        </span>
                      <% elsif playground.track_party %>
                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-900/60 to-purple-800/60 text-purple-200 border border-purple-600/30 shadow-sm">
                          <%= icon "target", library: "lucide", class: "h-3 w-3 mr-1.5" %>
                          <%= playground.track_party.name %>
                        </span>
                      <% else %>
                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-gray-800/60 text-gray-400 border border-gray-600/30">
                          <%= icon "minus", library: "lucide", class: "h-3 w-3 mr-1.5" %>
                          Unassigned
                        </span>
                      <% end %>
                    </td>
                    <td class="px-6 py-4">
                      <div class="flex items-center justify-end space-x-1">
                        <%= link_to api_playground_path(playground.slug),
                                    class: "group/btn inline-flex items-center p-2 text-primary-400 hover:text-primary-300 hover:bg-primary-900/30 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary-500/20",
                                    title: "Test API" do %>
                          <%= icon "play", library: "lucide", class: "h-4 w-4 group-hover/btn:scale-110 transition-transform" %>
                        <% end %>
                        <%= link_to edit_api_playground_path(playground.slug),
                                    class: "group/btn inline-flex items-center p-2 text-gray-400 hover:text-gray-300 hover:bg-gray-700/50 rounded-lg transition-all duration-200",
                                    title: "Edit" do %>
                          <%= icon "pencil", library: "lucide", class: "h-4 w-4 group-hover/btn:scale-110 transition-transform" %>
                        <% end %>
                        <%= link_to api_playground_path(playground.slug),
                                    data: {
                                      turbo_method: :delete,
                                      turbo_confirm: "Are you sure you want to delete this API Playground?",
                                    },
                                    class: "group/btn inline-flex items-center p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-red-500/20",
                                    title: "Delete" do %>
                          <%= icon "trash-2", library: "lucide", class: "h-4 w-4 group-hover/btn:scale-110 transition-transform" %>
                        <% end %>
                      </div>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      <% else %>
        <!-- Empty State -->
        <div class="relative overflow-hidden bg-gradient-to-br from-gray-800/60 to-gray-700/40 rounded-2xl border border-gray-700/50 p-12 text-center backdrop-blur-sm">
          <div class="absolute inset-0 bg-gradient-to-r from-primary-900/5 via-transparent to-primary-900/5"></div>
          <div class="relative flex flex-col items-center">
            <div class="w-20 h-20 bg-gradient-to-br from-gray-700 to-gray-800 rounded-2xl flex items-center justify-center mb-6 shadow-xl">
              <%= icon "zap", library: "lucide", class: "h-10 w-10 text-gray-400" %>
            </div>
            <h3 class="text-xl font-bold text-white mb-2">No API Playgrounds yet</h3>
            <p class="text-gray-400 max-w-md mb-8 leading-relaxed">
              Create your first API configuration to start testing and managing your API endpoints.
              Build, test, and iterate on your integrations with ease.
            </p>
            <%= link_to new_api_playground_path, class: "group relative inline-flex items-center overflow-hidden rounded-xl bg-gradient-to-r from-primary-600 to-primary-700 px-8 py-4 text-base font-semibold text-white shadow-lg transition-all duration-300 hover:from-primary-500 hover:to-primary-600 hover:shadow-xl hover:shadow-primary-500/25 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900" do %>
              <div class="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
              <%= icon "plus", library: "lucide", class: "h-5 w-5 mr-3 transition-transform group-hover:scale-110" %>
              Create Your First Playground
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>