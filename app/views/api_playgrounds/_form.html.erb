<%= form_with(model: api_playground, local: true) do |form| %>
  <div class="space-y-6">
    <!-- Basic Information -->
    <div class="bg-gray-900 overflow-hidden rounded-xl border border-gray-800 shadow-sm">
      <div class="px-6 py-5 border-b border-gray-800">
        <h3 class="text-base font-semibold leading-6 text-white">Basic Information</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-400">
          Essential details about the API playground configuration.
        </p>
      </div>
      
      <div class="px-6 py-6 space-y-6">
        <!-- Name Field -->
        <div>
          <%= form.label :name, class: "block text-sm font-medium text-gray-300 mb-2" %>
          <%= form.text_field :name, 
                              class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm",
                              placeholder: "Example: Applovin API" %>
          <% if api_playground.errors[:name].any? %>
            <p class="mt-2 text-sm text-red-400">
              <%= api_playground.errors[:name].first %>
            </p>
          <% end %>
        </div>

        <!-- Description Field -->
        <div>
          <%= form.label :description, class: "block text-sm font-medium text-gray-300 mb-2" %>
          <%= form.text_area :description, 
                             rows: 4,
                             class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm",
                             placeholder: "Describe the purpose and functionality of this API..." %>
          <% if api_playground.errors[:description].any? %>
            <p class="mt-2 text-sm text-red-400">
              <%= api_playground.errors[:description].first %>
            </p>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Associations -->
    <div class="bg-gray-900 overflow-hidden rounded-xl border border-gray-800 shadow-sm">
      <div class="px-6 py-5 border-b border-gray-800">
        <h3 class="text-base font-semibold leading-6 text-white">Associations</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-400">
          Link this API playground to relevant clients, partners, or track parties.
        </p>
      </div>
      
      <div class="px-6 py-6 space-y-6">
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <!-- Client Field -->
          <div>
            <%= form.label :client_id, "Associated Client", class: "block text-sm font-medium text-gray-300 mb-2" %>
            <%= form.collection_select :client_id, Client.active.order(name: :asc), :id, :name,
                                       { prompt: "Select a client (optional)" },
                                       { class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" } %>
            <% if api_playground.errors[:client_id].any? %>
              <p class="mt-2 text-sm text-red-400">
                <%= api_playground.errors[:client_id].first %>
              </p>
            <% end %>
          </div>

          <!-- Partner Field -->
          <div>
            <%= form.label :partner_id, "Associated Partner", class: "block text-sm font-medium text-gray-300 mb-2" %>
            <%= form.collection_select :partner_id, Partner.active.order(name: :asc), :id, :name,
                                       { prompt: "Select a partner (optional)" },
                                       { class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" } %>
            <% if api_playground.errors[:partner_id].any? %>
              <p class="mt-2 text-sm text-red-400">
                <%= api_playground.errors[:partner_id].first %>
              </p>
            <% end %>
          </div>
        </div>

        <!-- Track Party Field -->
        <div>
          <%= form.label :track_party_id, "Associated Track Party", class: "block text-sm font-medium text-gray-300 mb-2" %>
          <%= form.collection_select :track_party_id, TrackParty.all, :id, :name,
                                     { prompt: "Select a track party (optional)" },
                                     { class: "block w-full rounded-lg border-gray-600 bg-gray-800 text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" } %>
          <% if api_playground.errors[:track_party_id].any? %>
            <p class="mt-2 text-sm text-red-400">
              <%= api_playground.errors[:track_party_id].first %>
            </p>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="bg-gray-900 overflow-hidden rounded-xl border border-gray-800 shadow-sm sticky bottom-0 z-10">
      <div class="px-6 py-5 bg-gradient-to-r from-gray-800 to-gray-900">
        <div class="flex items-center justify-between">
          <!-- Left side - Form status/info -->
          <div class="flex items-center space-x-4">
            <% if api_playground.persisted? %>
              <div class="flex items-center text-sm text-gray-400">
                <%= icon "clock", class: "mr-1.5 h-4 w-4" %>
                Last updated <%= time_ago_in_words(api_playground.updated_at) %> ago
              </div>
            <% else %>
              <div class="flex items-center text-sm text-gray-400">
                <%= icon "plus-circle", class: "mr-1.5 h-4 w-4" %>
                Creating new API playground
              </div>
            <% end %>
          </div>

          <!-- Right side - Action buttons -->
          <div class="flex items-center space-x-3">
            <%= link_to api_playground.persisted? ? api_playground_path(api_playground.slug) : api_playgrounds_path, 
                        class: "inline-flex items-center px-5 py-2.5 border border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-300 bg-gray-800 hover:bg-gray-700 hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out" do %>
              <%= icon "x-mark", class: "mr-2 h-4 w-4" %>
              Cancel
            <% end %>
            
            <% if api_playground.persisted? %>
              <%= form.submit "Update API Playground", 
                              class: "cursor-pointer inline-flex items-center px-6 py-2.5 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98]",
                              data: { disable_with: "#{icon('arrow-path', class: 'animate-spin mr-2 h-4 w-4')} Updating...".html_safe } %>
            <% else %>
              <%= form.submit "Create API Playground", 
                              class: "cursor-pointer inline-flex items-center px-6 py-2.5 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98]",
                              data: { disable_with: "#{icon('arrow-path', class: 'animate-spin mr-2 h-4 w-4')} Creating...".html_safe } %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>