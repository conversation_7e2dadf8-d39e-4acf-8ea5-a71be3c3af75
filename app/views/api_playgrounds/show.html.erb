<div class="w-full h-full flex flex-col border border-slate-800 overflow-hidden bg-slate-900 shadow-2xl" data-controller="ruby-wasm" data-ruby-wasm-language-value="ruby" data-ruby-wasm-code-value="<%= @api_playground&.code %>">

  <!-- Header with API Info and Controls -->
  <div class="flex items-center justify-between px-6 py-4 bg-gradient-to-r from-slate-800 to-slate-900 border-b border-slate-700 shadow-lg">
    <div class="flex items-center gap-4">
      <h2 class="text-xl font-bold text-white tracking-tight">
        <%= @api_playground&.name || "API Playground" %>
      </h2>
      <% if @api_playground&.description.present? %>
        <span class="text-xs text-slate-300 bg-slate-700/60 backdrop-blur-sm px-3 py-1.5 rounded-full border border-slate-600/50">
          <%= @api_playground.description %>
        </span>
      <% end %>
    </div>

    <div class="flex items-center gap-3">
      <%= link_to api_playgrounds_path,
                  class: "inline-flex items-center gap-x-2 rounded-lg bg-slate-700/80 backdrop-blur-sm px-4 py-2 text-sm font-medium text-slate-200 hover:bg-slate-600/80 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl border border-slate-600/50 hover:border-slate-500/50" do %>
        <%= icon "arrow-left", library: "lucide", class: "h-4 w-4" %>
        <span>Back</span>
      <% end %>
      <button
        data-action="click->ruby-wasm#saveCode"
        class="cursor-pointer rounded-lg px-4 py-2 bg-gradient-to-r from-emerald-600 to-teal-600 text-white text-sm font-semibold hover:from-emerald-700 hover:to-teal-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2 border border-emerald-500/30 hover:border-emerald-400/50"
      >
       <%= icon "save", library: "lucide", class: "h-4 w-4" %>
        <span>Save</span>
      </button>
      <button
        data-action="click->ruby-wasm#runCode"
        class="cursor-pointer rounded-lg px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-sm font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2 border border-blue-500/30 hover:border-blue-400/50"
      >
        <%= icon "play-circle", class: "h-4 w-4", variant: "solid" %>
        <span>Run</span>
      </button>
    </div>
  </div>

  <!-- Resizable Container -->
  <div class="flex-1 flex flex-col overflow-hidden bg-slate-900" id="resizable-container">
    <!-- Code Editor Area -->
    <div class="w-full overflow-hidden bg-slate-900 border-b border-slate-700/50" id="editor-container" style="height: 60%;">
      <pre id="editor" data-ruby-wasm-target="editor" class="h-full w-full bg-slate-900"></pre>
    </div>

    <!-- Resizable Divider -->
    <div class="w-full h-2 bg-gradient-to-r from-slate-700 to-slate-800 cursor-row-resize hover:from-slate-600 hover:to-slate-700 transition-all duration-200 shadow-inner" id="resize-handle">
      <div class="w-full h-full flex items-center justify-center">
        <div class="w-12 h-1 bg-slate-500 rounded-full hover:bg-slate-400 transition-colors shadow-sm"></div>
      </div>
    </div>

    <!-- Output Area -->
    <div class="w-full bg-black overflow-hidden flex flex-col border-t border-slate-700/50 shadow-inner" id="output-container" style="height: 40%;">
      <div class="flex items-center justify-between px-4 py-3 border-b border-slate-700/50 flex-shrink-0 bg-gradient-to-r from-slate-900 to-black">
        <div class="flex items-center gap-3">
          <span class="text-sm text-slate-100 font-semibold flex items-center gap-2">
            <%= icon "terminal", library: "lucide", class: "h-4 w-4 text-emerald-400" %>
            Output
          </span>
          <button
            data-action="click->ruby-wasm#clearOutput"
            class="cursor-pointer rounded-md px-3 py-1.5 text-xs text-slate-300 hover:text-white hover:bg-slate-700/60 transition-all duration-200 border border-slate-600/50 hover:border-slate-500/50 backdrop-blur-sm"
          >
            Clear
          </button>
        </div>
      </div>
      <div class="p-4 overflow-auto h-full bg-black" id="output-content">
        <pre data-ruby-wasm-target="output" class="font-mono text-sm text-slate-100 whitespace-pre-wrap leading-relaxed"></pre>
      </div>
    </div>
  </div>
</div>
