import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["campaignOption"]

  connect() {
    console.log("Campaign selector controller connected")
    this.selectedCountries = new Set()
    this.isEditMode = this.element.closest('form')?.querySelector('input[name="_method"][value="patch"]') !== null

    if (this.isEditMode) {
      this.initializeFromExistingSelections()
    }
  }

  toggleCountry(event) {
    const country = event.currentTarget.dataset.country

    if (this.selectedCountries.has(country)) {
      this.selectedCountries.delete(country)
    } else {
      this.selectedCountries.add(country)
    }

    this.updateCountryButtonStates()

    this.updateCampaignSelections()
  }

  clearSelection() {
    // Clear all country selections
    this.selectedCountries.clear()

    // Update button states
    this.updateCountryButtonStates()

    this.campaignOptionTargets.forEach(option => {
      const checkbox = option.querySelector('input[type="checkbox"]')
      if (checkbox) {
        checkbox.checked = false
      }
    })
  }

  updateCountryButtonStates() {
    const buttons = this.element.querySelectorAll('[data-action*="toggleCountry"]')

    buttons.forEach(button => {
      const country = button.dataset.country
      if (this.selectedCountries.has(country)) {
        button.className = 'px-3 py-1 text-sm rounded-full border border-gray-300 dark:border-gray-600 bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500'
      } else {
        button.className = 'px-3 py-1 text-sm rounded-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500'
      }
    })
  }

  updateCampaignSelections() {
    this.campaignOptionTargets.forEach(option => {
      const campaignCountry = option.dataset.country || ''
      const checkbox = option.querySelector('input[type="checkbox"]')

      if (this.selectedCountries.size === 0) {
        option.style.display = ''
        if (!this.isEditMode && checkbox) {
          checkbox.checked = false
        }
      } else {
        if (this.selectedCountries.has(campaignCountry)) {
          option.style.display = ''
          if (checkbox) {
            checkbox.checked = true
          }
        } else {
          option.style.display = 'none'
          if (checkbox) {
            checkbox.checked = false
          }
        }
      }
    })
  }

  toggleAll(event) {
    const button = event.currentTarget;
    const countryButtons = this.element.querySelectorAll('[data-action*="toggleCountry"]');
    const allCountries = Array.from(countryButtons).map(button => button.dataset.country);

    const allSelected = allCountries.length > 0 && allCountries.every(country => this.selectedCountries.has(country));

    if (allSelected) {
      this.selectedCountries.clear();

      this.campaignOptionTargets.forEach(option => {
        option.style.display = ''
        const checkbox = option.querySelector('input[type="checkbox"]')
        if (checkbox) {
          checkbox.checked = false
        }
      })
    } else {
      allCountries.forEach(country => this.selectedCountries.add(country));

      this.updateCampaignSelections();
    }

    this.updateCountryButtonStates();

    if (allSelected) {
      button.textContent = 'Select All';
      button.className = 'px-3 py-1 text-sm rounded-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500';
    } else {
      button.textContent = 'Deselect All';
      button.className = 'px-3 py-1 text-sm rounded-full border border-gray-300 dark:border-gray-600 bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500';
    }
  }

  initializeFromExistingSelections() {
    const checkedCampaigns = this.campaignOptionTargets.filter(option => {
      const checkbox = option.querySelector('input[type="checkbox"]')
      return checkbox && checkbox.checked
    })

    checkedCampaigns.forEach(option => {
      const country = option.dataset.country
      if (country) {
        this.selectedCountries.add(country)
      }
    })

    this.updateCountryButtonStates()
  }

  toggleBudgetDetails(event) {
    const elementId = event.currentTarget.dataset.target;
    const element = document.getElementById(elementId);
    const button = event.currentTarget;

    if (element.classList.contains('hidden')) {
      element.classList.remove('hidden');
      button.innerHTML = button.innerHTML.replace('▼', '▲');
    } else {
      element.classList.add('hidden');
      button.innerHTML = button.innerHTML.replace('▲', '▼');
    }
  }
}