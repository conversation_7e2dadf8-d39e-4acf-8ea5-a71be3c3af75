require "ostruct"

class CampaignSpendsQuery
  include ActiveModel::Model
  include ActiveModel::Attributes

  attribute :date_gteq, :string, default: -> { Date.yesterday.beginning_of_month.to_s }
  attribute :date_lteq, :string, default: -> { Date.yesterday.to_s }
  attribute :limit, :integer, default: 20
  attribute :sort_by, :string, default: "date_desc"
  attribute :export_type, :string, default: "client"
  attribute :spend_type_in, default: []
  attribute :hide_test_data, :boolean, default: true
  attribute :legacy_campaign_id_in, default: []
  attribute :legacy_partner_id_in, default: []
  attribute :legacy_client_id_in, default: []
  attribute :legacy_click_url_id_in, default: []
  attribute :platform

  def results
    scope = CampaignSpendSummary.includes(:campaign, :client, :partner, :gross_campaign_spend, :net_campaign_spend)

    sort(scope)
      .then { filter_by_dates(_1) }
      .then { filter_by_partner(_1) }
      .then { filter_by_client(_1) }
      .then { filter_by_click_url(_1) }
      .then { filter_by_campaign(_1) }
      .then { filter_by_platform(_1) }
      .then { filter_by_test_data(_1) }
  end

  def aggregated_results_including_events(level: :client)
    group_columns = {
      client: [ :legacy_client_id, "clients.name" ],
      partner: [ "campaign_spend_summaries.legacy_partner_id", "partners.name" ],
      campaign: [ "campaign_spend_summaries.legacy_campaign_id", "campaigns.name" ]
    }[level]

    scope = results.joins(level)

    results = scope
      .group(*group_columns, "DATE(campaign_spend_summaries.spend_date)")
      .pluck(
        *group_columns,
        "DATE(campaign_spend_summaries.spend_date) as date",
        "SUM(gross_spend) as gross",
        "SUM(adjusted_net_spend) as net_spend",
        "SUM(impression_count) as impressions",
        "SUM(click_count) as clicks",
        "SUM(install_count) as installs",
        "SUM(revenue) as revenues"
      )
      .each_with_object({}) do |(id, name, date, gross, net, impressions, clicks, installs, revenues), hash|
        gross ||= 0
        net ||= 0
        impressions ||= 0
        clicks ||= 0
        installs ||= 0
        revenues ||= 0

        hash[id] ||= {
          "#{level}_name": name,
          gross: 0,
          net: 0,
          impressions: 0,
          clicks: 0,
          installs: 0,
          revenues: 0,
          daily: {}
        }

        # Add to total sums
        hash[id][:gross] += gross
        hash[id][:net] += net
        hash[id][:impressions] += impressions.to_i
        hash[id][:clicks] += clicks.to_i
        hash[id][:installs] += installs.to_i
        hash[id][:revenues] += revenues
        hash[id][:margin] = calculated_margin(hash[id][:gross], hash[id][:net])
      end.transform_values do |data|
        data
      end

    { results: results }
  end

  def calculated_margin(gross_spend, net_spend)
    gross_spend > 0 ? (1 - net_spend.to_f / gross_spend) * 100 : nil
  end

  def trend_data
    if legacy_click_url_id_in.reject(&:empty?).present?
      group_by_click_urls(results)
    elsif legacy_campaign_id_in.reject(&:empty?).present?
      group_by_campaigns(results)
    elsif legacy_client_id_in.reject(&:empty?).present?
      group_by_clients(results)
    elsif legacy_partner_id_in.reject(&:empty?).present?
      group_by_partners(results)
    else
      [
        {
          name: "Gross Spend",
          data: group_daily_amounts(results, :gross_spend)
        }
      ]
    end
  end

  def clients
    legacy_client_ids = filter_by_dates(CampaignSpendSummary)
      .then { filter_by_test_data(_1) }
      .pluck(:legacy_client_id).uniq
    Client.where(legacy_id: legacy_client_ids).order(:name)
  end

  def partners
    legacy_partner_ids = filter_by_dates(CampaignSpendSummary)
      .then { filter_by_test_data(_1) }
      .pluck(:legacy_partner_id).uniq
    Partner.where(legacy_id: legacy_partner_ids).order(:name)
  end

  def legacy_click_url_ids
    legacy_click_url_ids = filter_by_dates(CampaignSpendSummary)
      .then { filter_by_test_data(_1) }
      .then { filter_by_partner(_1) }
      .then { filter_by_client(_1) }
      .pluck(:legacy_click_url_id)
      .uniq
      .sort

    legacy_click_url_ids.map { |id| OpenStruct.new(legacy_id: id) }
  end

  def campaigns
    legacy_campaign_ids = filter_by_dates(CampaignSpendSummary)
      .then { filter_by_test_data(_1) }
      .then { filter_by_partner(_1) }
      .then { filter_by_client(_1) }
      .then { filter_by_platform(_1) }
      .pluck(:legacy_campaign_id)
      .uniq

    Campaign.where(legacy_id: legacy_campaign_ids).order(:name)
  end

  private

  def sort(scope)
    case sort_by
    when "date_desc"
      scope.order(spend_date: :desc, legacy_campaign_id: :asc)
    when "date_asc"
      scope.order(spend_date: :asc, legacy_campaign_id: :asc)
    when "gross_spend_desc"
      scope.order(gross_spend: :desc)
    when "gross_spend_asc"
      scope.order(gross_spend: :asc)
    when "net_spend_desc"
      scope.order(adjusted_net_spend: :desc)
    when "net_spend_asc"
      scope.order(adjusted_net_spend: :asc)
    when "margin_desc"
      scope.order(adjusted_margin: :desc)
    when "margin_asc"
      scope.order(adjusted_margin: :asc)
    when "client"
      scope.order("clients.name asc")
    when "partner"
      scope.order("partners.name asc")
    when "campaign"
      scope.order("campaigns.name asc")
    else
      scope
    end
  end

  def filter_by_attribute(scope, attribute, values)
    return scope if values.blank?

    filtered_values = Array(values).reject(&:blank?)
    filtered_values.any? ? scope.where(attribute => filtered_values) : scope
  end

  def filter_by_campaign(scope)
    filter_by_attribute(scope, :legacy_campaign_id, legacy_campaign_id_in)
  end

  def filter_by_client(scope)
    filter_by_attribute(scope, :legacy_client_id, legacy_client_id_in)
  end

  def filter_by_click_url(scope)
    filter_by_attribute(scope, :legacy_click_url_id, legacy_click_url_id_in)
  end

  def filter_by_partner(scope)
    filter_by_attribute(scope, :legacy_partner_id, legacy_partner_id_in)
  end

  def filter_by_dates(scope)
    if date_gteq = self.date_gteq.presence
      scope = scope.where("spend_date >= ?", date_gteq)
    end

    if date_lteq = self.date_lteq.presence
      scope = scope.where("spend_date <= ?", date_lteq)
    end

    scope
  end

  def filter_by_platform(scope)
    return scope if platform.blank?

    scope.where(
      "EXISTS (SELECT 1 FROM campaigns WHERE campaigns.legacy_id = #{scope.table_name}.legacy_campaign_id AND campaigns.name ILIKE ?)",
      "%#{platform}%"
    )
  end

  def filter_by_test_data(scope)
    if hide_test_data
      scope = scope.where(is_test: false)
    end

    scope
  end

  def group_by_clients(results)
    trends = []
    results.group_by(&:legacy_client_id).each do |client_id, spends|
      client_name = Client.find_by!(legacy_id: client_id).name
      trends.concat([
        {
          name: "#{client_name} (Gross)",
          data: group_daily_amounts(spends, :gross_spend)
        }
      ])
    end
    trends
  end

  def group_by_partners(results)
    trends = []
    results.group_by(&:legacy_partner_id).each do |partner_id, spends|
      partner_name = Partner.find_by!(legacy_id: partner_id).name
      trends.concat([
        {
          name: "#{partner_name} (Net)",
          data: group_daily_amounts(spends, :net_spend)
        }
      ])
    end
    trends
  end

  def group_by_campaigns(results)
    trends = []
    grouped_results = results.group_by(&:legacy_campaign_id)

    grouped_results.each do |campaign_id, spends|
      campaign_name = Campaign.find_by!(legacy_id: campaign_id).name
      trends << {
        name: "#{campaign_name} (Gross)",
        data: group_daily_amounts(spends, :gross_spend)
      }

      # Only add net spend if there's exactly one campaign
      if grouped_results.size == 1
        trends << {
          name: "#{campaign_name} (Net)",
          data: group_daily_amounts(spends, :net_spend)
        }
      end
    end
    trends
  end

  def group_by_click_urls(results)
    trends = []
    grouped_results = results.group_by(&:legacy_click_url_id)

    grouped_results.each do |click_url_id, spends|
      click_url = ClickUrl.find_by(legacy_id: click_url_id)
      trends << {
        name: "#{click_url.legacy_id} (Gross)",
        data: group_daily_amounts(spends, :gross_spend)
      }

      # Only add net spend if there's exactly one click URL
      if grouped_results.size == 1
        trends << {
          name: "#{click_url.legacy_id} (Net)",
          data: group_daily_amounts(spends, :net_spend)
        }
      end
    end
    trends
  end

  def group_daily_amounts(spends, method = nil, start_date: nil, end_date: nil, default_value: 0)
    raise ArgumentError, "spends cannot be nil" if spends.nil?

    # Convert spends to hash with date as key
    grouped_spends = spends.group_by(&:spend_date)

    # Determine date range
    start_date ||= grouped_spends.keys.min
    end_date ||= grouped_spends.keys.max

    return [] if start_date.nil? || end_date.nil?

    raise ArgumentError, "start_date cannot be after end_date" if start_date > end_date

    # Generate array of all dates in range
    (start_date..end_date).map do |date|
      daily_spends = grouped_spends[date] || []
      amount = if method
        daily_spends.sum(&method)
      elsif block_given?
        daily_spends.sum { |spend| yield(spend) }
      else
        default_value
      end
      [ date, amount ]
    end
  rescue Date::Error => e
    raise ArgumentError, "Invalid date format: #{e.message}"
  end
end
