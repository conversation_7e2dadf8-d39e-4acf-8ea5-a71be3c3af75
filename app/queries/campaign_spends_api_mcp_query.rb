require "ostruct"

class CampaignSpendsApiMcpQuery
  include ActiveModel::Model
  include ActiveModel::Attributes

  CampaignEntry = Struct.new(
    :name,
    :gross,
    :net,
    :revenue,
    :impressions,
    :clicks,
    :installs,
    keyword_init: true
  )

  attribute :date_gteq, :string, default: -> { Date.yesterday.beginning_of_month.to_s }
  attribute :date_lteq, :string, default: -> { Date.yesterday.to_s }
  attribute :legacy_campaign_id_in, default: []
  attribute :legacy_partner_id_in, default: []
  attribute :legacy_client_id_in, default: []
  attribute :legacy_click_url_id_in, default: []
  attribute :groups, default: [ "campaign", "partner" ]
  attribute :metrics, default: [ "gross", "net" ]

  def initialize(attributes = nil)
    super
    sanitize_date_attributes
  end

  # Method to sanitize date attributes right after initialization
  def sanitize_date_attributes
    self.date_gteq = ensure_valid_date(date_gteq) if date_gteq.present?
    self.date_lteq = ensure_valid_date(date_lteq) if date_lteq.present?
  end

  def results
    scope = DailyCampaignSpend.includes(:campaign, :client, :partner)

    # Define all possible group columns
    all_group_columns = {
      client: "clients.name",
      partner: "partners.name",
      campaign: "campaigns.name",
      country: "campaigns.country_code",
      click_url: :legacy_click_url_id,
      day: "DATE(daily_campaign_spends.spend_date)",
      week: Arel.sql("DATE_TRUNC('week', daily_campaign_spends.spend_date)"),
      month: Arel.sql("DATE_TRUNC('month', daily_campaign_spends.spend_date)")
    }

    # Select group columns based on the groups array
    selected_group_columns = Array(groups).map do |group|
      all_group_columns[group.to_sym]
    end.compact

    scope = scope
      .where(is_test: false)
      .then { filter_by_dates(_1) }
      .then { filter_by_client(_1) }
      .then { filter_by_partner(_1) }
      .then { filter_by_campaign(_1) }
      .then { filter_by_click_url(_1) }

    # Dynamically add joins based on selected group keys
    Array(groups).each do |group|
      if [ "client", "partner", "campaign" ].include?(group.to_s)
        scope = scope.joins(group.to_sym)
      end
    end

    results = scope
      .group(*selected_group_columns)
      .order("SUM(gross_spend) DESC")
      .pluck(
        *selected_group_columns,
        "SUM(gross_spend) as gross",
        "SUM(adjusted_net_spend) as net",
        "SUM(revenue)",
        "SUM(impression_count) as impressions",
        "SUM(click_count) as clicks",
        "SUM(install_count) as installs"
      )
      .map do |*group_names, gross, net, revenue, impressions, clicks, installs| # Adjusted to handle multiple group names
        row = {
          gross: gross.to_f,
          net: net.to_f,
          revenue: revenue.to_f,
          impressions: impressions.to_i,
          clicks: clicks.to_i,
          installs: installs.to_i,
          cvr: calculate_cvr(clicks.to_i, installs.to_i),
          margin: calculate_margin(gross.to_f, net.to_f)
        }.select { |key, _| metrics.map(&:to_sym).include?(key) }

        selected_group_columns.each_with_index do |col, i|
          key = all_group_columns.select { |_, v| v == col }.keys.first
          row[key] = group_names[i]
        end

        row
      end

    results
  end

  def campaigns
    legacy_campaign_ids = filter_by_dates(DailyCampaignSpend.where(is_test: false))
      .then { filter_by_partner(_1) }
      .then { filter_by_client(_1) }
      .pluck(:legacy_campaign_id)
      .uniq

    Campaign.where(legacy_id: legacy_campaign_ids)
  end

  private

  def calculate_cvr(clicks, installs)
    clicks&.positive? ? (installs.to_f / clicks * 100).round(2) : 0
  end

  def calculate_margin(gross, net)
    gross&.positive? ? ((gross - net) / gross * 100).round(2) : 0
  end

  def filter_by_attribute(scope, attribute, values)
    return scope if values.blank?

    filtered_values = Array(values).reject(&:blank?)
    filtered_values.any? ? scope.where(attribute => filtered_values) : scope
  end

  def filter_by_campaign(scope)
    filter_by_attribute(scope, :legacy_campaign_id, legacy_campaign_id_in)
  end

  def filter_by_client(scope)
    filter_by_attribute(scope, :legacy_client_id, legacy_client_id_in)
  end

  def filter_by_click_url(scope)
    filter_by_attribute(scope, :legacy_click_url_id, legacy_click_url_id_in)
  end

  def filter_by_partner(scope)
    filter_by_attribute(scope, :legacy_partner_id, legacy_partner_id_in)
  end

  def filter_by_dates(scope)
    if date_gteq = self.date_gteq.presence
      scope = scope.where("spend_date >= ?", date_gteq)
    end

    if date_lteq = self.date_lteq.presence
      scope = scope.where("spend_date <= ?", date_lteq)
    end

    scope
  end

  def ensure_valid_date(date_str)
    return date_str if date_str.is_a?(Date)

    # Try to create a valid date
    begin
      Date.parse(date_str).to_s
    rescue Date::Error
      # Try to parse the date string
      match = date_str.to_s.match(/(\d+)-(\d+)-(\d+)/)
      return date_str unless match

      year = match[1].to_i
      month = match[2].to_i

      Date.new(year, month).end_of_month.to_s
    end
  end
end
