require "ostruct"

class CampaignMonthlySpendsQuery
  include ActiveModel::Model
  include ActiveModel::Attributes

  attribute :start_month, :string, default: -> { Date.yesterday.beginning_of_year.strftime("%Y-%m") }
  attribute :end_month, :string, default: -> { "#{Date.today.year}-12" }
  attribute :limit, :integer, default: 20
  attribute :sort_by, :string, default: "date_desc"
  attribute :spend_type_in, default: []
  attribute :hide_test_data, :boolean, default: true
  attribute :legacy_campaign_id_in, default: []
  attribute :legacy_partner_id_in, default: []
  attribute :legacy_client_id_in, default: []
  attribute :platform

  def results
    scope = MonthlyCampaignSpend.includes(:campaign, :client, :partner)

    sort(scope)
      .then { filter_by_months(_1) }
      .then { filter_by_partner(_1) }
      .then { filter_by_client(_1) }
      .then { filter_by_campaign(_1) }
      .then { filter_by_platform(_1) }
      .then { filter_by_test_data(_1) }
  end

  def trend_data
    if legacy_campaign_id_in.reject(&:empty?).present?
      group_by_campaigns(results)
    elsif legacy_client_id_in.reject(&:empty?).present?
      group_by_clients(results)
    elsif legacy_partner_id_in.reject(&:empty?).present?
      group_by_partners(results)
    else
      [
        {
          name: "Gross Spend",
          data: group_monthly_amounts(results, :gross_spend)
        },
        {
          name: "Revenue",
          data: group_monthly_amounts(results, :adjusted_revenue)
        }
      ]
    end
  end

  def year_trend_data
    filter_target_data = target_trend_data
    target_revenue = filter_target_data.first[:data]
    target_margin = filter_target_data.last[:data]

    [
      {
        name: "Gross Target",
        data: target_revenue.transform_keys { |date_str| Date.parse(date_str) },
        library: line_with_dashed_border(3, 3)
      },
      {
        name: "Revenue Target",
        data: target_margin.transform_keys { |date_str| Date.parse(date_str) },
        library: line_with_dashed_border(3, 3)
      }
    ]
  end

  def line_with_dashed_border(line_length, space_length)
    {
      elements: {
        line: {
          borderDash: [ line_length, space_length ]
        }
      }
    }
  end

  def clients
    legacy_client_ids = filter_by_months(MonthlyCampaignSpend)
      .then { filter_by_test_data(_1) }
      .pluck(:legacy_client_id).uniq
    Client.where(legacy_id: legacy_client_ids).order(:name)
  end

  def partners
    legacy_partner_ids = filter_by_months(MonthlyCampaignSpend)
      .then { filter_by_test_data(_1) }
      .pluck(:legacy_partner_id).uniq
    Partner.where(legacy_id: legacy_partner_ids).order(:name)
  end

  def campaigns
    legacy_campaign_ids = filter_by_months(MonthlyCampaignSpend)
      .then { filter_by_test_data(_1) }
      .then { filter_by_partner(_1) }
      .then { filter_by_client(_1) }
      .then { filter_by_platform(_1) }
      .pluck(:legacy_campaign_id)
      .uniq

    Campaign.where(legacy_id: legacy_campaign_ids).order(:name)
  end

  private

  def sort(scope)
    case sort_by
    when "date_desc"
      scope.order(month_date: :desc, legacy_campaign_id: :asc)
    when "date_asc"
      scope.order(month_date: :asc, legacy_campaign_id: :asc)
    when "gross_spend_desc"
      scope.order(adjusted_gross_spend: :desc)
    when "gross_spend_asc"
      scope.order(adjusted_gross_spend: :asc)
    when "net_spend_desc"
      scope.order(adjusted_net_spend: :desc)
    when "net_spend_asc"
      scope.order(adjusted_net_spend: :asc)
    when "margin_desc"
      scope.order(adjusted_margin: :desc)
    when "margin_asc"
      scope.order(adjusted_margin: :asc)
    when "revenue_asc"
      scope.order(adjusted_revenue: :asc)
    when "revenue_desc"
      scope.order(adjusted_revenue: :desc)
    else
      scope
    end
  end

  def filter_by_attribute(scope, attribute, values)
    return scope if values.blank?

    filtered_values = Array(values).reject(&:blank?)
    filtered_values.any? ? scope.where(attribute => filtered_values) : scope
  end

  def filter_by_campaign(scope)
    filter_by_attribute(scope, :legacy_campaign_id, legacy_campaign_id_in)
  end

  def filter_by_client(scope)
    filter_by_attribute(scope, :legacy_client_id, legacy_client_id_in)
  end

  def filter_by_partner(scope)
    filter_by_attribute(scope, :legacy_partner_id, legacy_partner_id_in)
  end

  def filter_by_months(scope)
    if start_month = self.start_month.presence
      start_date = Date.parse("#{start_month}-01")
      scope = scope.where("month_date >= ?", start_date)
    end

    if end_month = self.end_month.presence
      end_date = Date.parse("#{end_month}-01").end_of_month
      scope = scope.where("month_date <= ?", end_date)
    end

    scope
  end

  def filter_by_test_data(scope)
    if hide_test_data
      scope = scope.where(is_test: false)
    end

    scope
  end

  def filter_by_platform(scope)
    return scope if platform.blank?

    scope.where(
      "EXISTS (SELECT 1 FROM campaigns WHERE campaigns.legacy_id = #{scope.table_name}.legacy_campaign_id AND campaigns.name ILIKE ?)",
      "%#{platform}%"
    )
  end

  def group_by_clients(results)
    trends = []
    results.group_by(&:legacy_client_id).each do |client_id, spends|
      client_name = Client.find_by!(legacy_id: client_id).name
      trends.concat([
        {
          name: "#{client_name} (Gross)",
          data: group_monthly_amounts(spends, :adjusted_gross_spend)
        }
      ])
    end
    trends
  end

  def group_by_partners(results)
    trends = []
    results.group_by(&:legacy_partner_id).each do |partner_id, spends|
      partner_name = Partner.find_by!(legacy_id: partner_id).name
      trends.concat([
        {
          name: "#{partner_name} (Net)",
          data: group_monthly_amounts(spends, :adjusted_net_spend)
        }
      ])
    end
    trends
  end

  def group_by_campaigns(results)
    trends = []
    grouped_results = results.group_by(&:legacy_campaign_id)

    grouped_results.each do |campaign_id, spends|
      campaign_name = Campaign.find_by!(legacy_id: campaign_id).name
      trends << {
        name: "#{campaign_name} (Gross)",
        data: group_monthly_amounts(spends, :adjusted_gross_spend)
      }

      # Only add adjusted amounts if there's exactly one campaign
      if grouped_results.size == 1
        trends.concat([
          {
            name: "#{campaign_name} (Net)",
            data: group_monthly_amounts(spends, :adjusted_net_spend)
          }
        ])
      end
    end
    trends
  end

  def group_monthly_amounts(spends, method = nil, start_date: nil, end_date: nil, default_value: 0)
    raise ArgumentError, "spends cannot be nil" if spends.nil?

    # Convert spends to hash with date as key
    grouped_spends = spends.group_by(&:month_date)

    # Determine date range
    start_date ||= Date.parse("#{start_month}-01") if start_month.present?
    end_date ||= Date.parse("#{end_month}-01").end_of_month if end_month.present?

    return [] if start_date.nil? || end_date.nil?

    raise ArgumentError, "start_date cannot be after end_date" if start_date > end_date

    # Generate array of all months in range
    start_month = start_date.beginning_of_month
    end_month = end_date.beginning_of_month

    (start_month..end_month).select { |d| d.day == 1 }.map do |date|
      daily_spends = grouped_spends[date] || []
      amount = if method
        daily_spends.sum(&method)
      elsif block_given?
        daily_spends.sum { |spend| yield(spend) }
      else
        default_value
      end
      [ date, amount ]
    end
  rescue Date::Error => e
    raise ArgumentError, "Invalid date format: #{e.message}"
  end

  def target_trend_data
    [
      {
        name: "Gross Revenue",
        data: {
          "2025-03-01" => 2125000,
          "2025-04-01" => 2350000,
          "2025-05-01" => 2500000,
          "2025-06-01" => 2750000,
          "2025-07-01" => 3000000,
          "2025-08-01" => 3250000,
          "2025-09-01" => 3400000,
          "2025-10-01" => 3550000,
          "2025-11-01" => 3750000,
          "2025-12-01" => 3875000
        }
      },
      {
        name: "Margin Target",
        data: {
          "2025-03-01" => 850000,
          "2025-04-01" => 940000,
          "2025-05-01" => 1000000,
          "2025-06-01" => 1100000,
          "2025-07-01" => 1200000,
          "2025-08-01" => 1300000,
          "2025-09-01" => 1360000,
          "2025-10-01" => 1420000,
          "2025-11-01" => 1500000,
          "2025-12-01" => 1550000
        }
      }
    ].map do |item|
      {
        name: item[:name],
        data: item[:data].select { |date, _value|
          (start_month.nil? || date >= "#{start_month}-01") && (end_month.nil? || date <= "#{end_month}-01")
        }
      }
    end
  end
end
