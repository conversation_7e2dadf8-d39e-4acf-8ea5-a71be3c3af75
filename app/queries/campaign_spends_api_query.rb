require "ostruct"

class CampaignSpendsApiQuery
  include ActiveModel::Model
  include ActiveModel::Attributes

  CampaignEntry = Struct.new(
    :name,
    :gross,
    :net,
    :revenue,
    :impressions,
    :clicks,
    :installs,
    keyword_init: true
  )

  attribute :date_gteq, :string, default: -> { Date.yesterday.beginning_of_month.to_s }
  attribute :date_lteq, :string, default: -> { Date.yesterday.to_s }
  attribute :legacy_campaign_id_in, default: []
  attribute :legacy_partner_id_in, default: []
  attribute :legacy_client_id_in, default: []
  attribute :legacy_click_url_id_in, default: []
  attribute :group_by, :string, default: "day"
  attribute :metrics, default: [ "gross", "net" ]

  def initialize(attributes = nil)
    super
    sanitize_date_attributes
  end

  # Method to sanitize date attributes right after initialization
  def sanitize_date_attributes
    self.date_gteq = ensure_valid_date(date_gteq) if date_gteq.present?
    self.date_lteq = ensure_valid_date(date_lteq) if date_lteq.present?
  end

  def results
    scope = CampaignSpendSummary.includes(:campaign, :client, :partner)

    group_columns = {
      client: [ "clients.name" ],
      partner: [ "partners.name" ],
      campaign: [ "campaigns.name" ],
      click_url: [ :legacy_click_url_id ],
      day: [ "DATE(campaign_spend_summaries.spend_date)" ],
      week: [ Arel.sql("DATE_TRUNC('week', campaign_spend_summaries.spend_date)") ],
      month: [ Arel.sql("DATE_TRUNC('month', campaign_spend_summaries.spend_date)") ]
    }[group_by.to_sym]

    scope = scope
      .where(is_test: false)
      .then { filter_by_dates(_1) }
      .then { filter_by_client(_1) }
      .then { filter_by_partner(_1) }
      .then { filter_by_campaign(_1) }
      .then { filter_by_click_url(_1) }

    if [ "client", "partner", "campaign", "click_url" ].include?(group_by.to_s)
      scope = scope.joins(group_by.to_sym)
    end

    results = scope
      .group(*group_columns)
      .pluck(
        *group_columns,
        "SUM(gross_spend) as gross",
        "SUM(adjusted_net_spend) as net",
        "SUM(revenue)",
        "SUM(impression_count) as impressions",
        "SUM(click_count) as clicks",
        "SUM(install_count) as installs"
      ).map do |name, gross, net, revenue, impressions, clicks, installs|
        CampaignEntry.new(
          name: name,
          gross: gross.to_f,
          net: net.to_f,
          revenue: revenue.to_f,
          impressions: impressions.to_i,
          clicks: clicks.to_i,
          installs: installs.to_i
        )
      end

    group_by_level(results, group_by.to_sym, metrics)
  end

  def campaigns
    legacy_campaign_ids = filter_by_dates(CampaignSpendSummary.where(is_test: false))
      .then { filter_by_partner(_1) }
      .then { filter_by_client(_1) }
      .pluck(:legacy_campaign_id)
      .uniq

    Campaign.where(legacy_id: legacy_campaign_ids)
  end

  private

  def group_by_level(entries, group_by, metrics = nil)
    result = {}

    entries.each do |entry|
      name = case group_by
      when :week
        entry.name.to_date.strftime("%Y-W%V")
      when :month
        entry.name.to_date.strftime("%Y-%m")
      else
        entry.name
      end

      result[name] ||= {
        gross: entry.gross,
        net: entry.net,
        revenue: entry.revenue,
        impressions: entry.impressions,
        clicks: entry.clicks,
        installs: entry.installs,
        cvr: calculate_cvr(entry.clicks, entry.installs),
        margin: calculate_margin(entry.gross, entry.net)
      }.select { |key, _| metrics.map(&:to_sym).include?(key) }
    end
    result
  end

  def calculate_cvr(clicks, installs)
    clicks&.positive? ? (installs.to_f / clicks * 100).round(2) : 0
  end

  def calculate_margin(gross, net)
    gross&.positive? ? ((gross - net) / gross * 100).round(2) : 0
  end

  def filter_by_attribute(scope, attribute, values)
    return scope if values.blank?

    filtered_values = Array(values).reject(&:blank?)
    filtered_values.any? ? scope.where(attribute => filtered_values) : scope
  end

  def filter_by_campaign(scope)
    filter_by_attribute(scope, :legacy_campaign_id, legacy_campaign_id_in)
  end

  def filter_by_client(scope)
    filter_by_attribute(scope, :legacy_client_id, legacy_client_id_in)
  end

  def filter_by_click_url(scope)
    filter_by_attribute(scope, :legacy_click_url_id, legacy_click_url_id_in)
  end

  def filter_by_partner(scope)
    filter_by_attribute(scope, :legacy_partner_id, legacy_partner_id_in)
  end

  def filter_by_dates(scope)
    if date_gteq = self.date_gteq.presence
      scope = scope.where("spend_date >= ?", date_gteq)
    end

    if date_lteq = self.date_lteq.presence
      scope = scope.where("spend_date <= ?", date_lteq)
    end

    scope
  end

  def ensure_valid_date(date_str)
    return date_str if date_str.is_a?(Date)

    # Try to create a valid date
    begin
      Date.parse(date_str).to_s
    rescue Date::Error
      # Try to parse the date string
      match = date_str.to_s.match(/(\d+)-(\d+)-(\d+)/)
      return date_str unless match

      year = match[1].to_i
      month = match[2].to_i

      Date.new(year, month).end_of_month.to_s
    end
  end
end
