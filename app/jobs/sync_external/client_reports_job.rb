module SyncExternal
  class ClientReportsJob < ApplicationJob
    queue_as :default

    def perform(report_name: nil, report_type: :rolling)
      if report_name.present?
        client_reports = ClientReport.where(report_name: report_name).all
      else
        client_reports = case report_type.to_sym
        when :monthly
          ClientReport.monthly.active.where(rolling_insert: false).all
        when :daily
          ClientReport.daily.active.where(rolling_insert: false).all
        when :rolling
          ClientReport.rolling.active.where(rolling_insert: false).all
        when :rolling_insert
          ClientReport.active.where(rolling_insert: true).all
        end
      end

      report_names = client_reports.pluck(:report_name)
      latest_reports = fetch_all_latest_reports(report_names, report_type)

      client_reports.find_each do |client_report|
        sync_reports(client_report, latest_reports[client_report.report_name] || [], report_type)
      end
    rescue => e
      raise e
    end

    private

    def sync_reports(client_report, reports, report_type)
      if reports.empty?
        client_report_file = client_report.client_report_files.create!(status: "failed", sync_log: "No reports found")
        return
      else
        client_report_file = client_report.client_report_files.create!(status: "pending", sync_log: "Syncing reports")
      end

      begin
        csv_contents = []
        reports.each do |report|
          ReportProcessors::ZipProcessor.process_url(report[:upload_path]) do |csv_content|
            csv_contents << csv_content
          end
        end

        csv_content = ReportProcessors::CsvMerger.merge(csv_contents, client_report.unique_keys.to_s.split(","), client_report.rolling_insert, client_report.date_keys.to_s.split(","))

        attach_csv_file(client_report, client_report_file, csv_content)

        client_report_file.update!(status: "ready", sync_log: "Report generated successfully")
      rescue => e
        client_report_file.update!(status: "failed", sync_log: e.message)
      end
    end

    def attach_csv_file(client_report, report_file, content)
      report_file.file.attach(
        io: content,
        filename: "#{client_report.report_name}_#{Time.now.strftime('%Y%m%d_%H%M%S')}.csv",
        content_type: "text/csv"
      )
    end

    def fetch_all_latest_reports(report_names, report_type)
      retries = 0
      max_retries = 3
      begin
        ExternalDatabase.main.fetch(
          fetch_sql_by_type(report_type),
          report_names
        ).all.group_by { |r| r[:client_name] }
      rescue Sequel::DatabaseError, PG::ConnectionBad, Sequel::DatabaseDisconnectError => e
        retries += 1
        if retries <= max_retries
          Rails.logger.warn("Database connection attempt #{retries} of #{max_retries} failed: #{e.message}")
          sleep(3 ** retries)
          retry
        else
          Rails.logger.error("Failed to fetch reports after #{max_retries} attempts: #{e.message}")
          raise
        end
      end
    end

    def fetch_sql_by_type(report_type)
      case report_type.to_sym
      when :monthly
        <<-SQL
          WITH date_range_parts AS (
            SELECT
              *,
              CASE
                WHEN date_range LIKE '% to %' THEN
                  SPLIT_PART(date_range, ' to ', 1)
                ELSE
                  date_range
              END as start_date,
              CASE
                WHEN date_range LIKE '% to %' THEN
                  SPLIT_PART(date_range, ' to ', 2)
                ELSE
                  date_range
              END as end_date
            FROM client_daily_report_logs
            WHERE status = 1
              AND upload_path IS NOT null
              AND date_range like '% to %'
              AND client_name IN ?
          ),
          ranked_reports AS (
            SELECT *,
            ROW_NUMBER() OVER (
              PARTITION BY client_name, date_range
              ORDER BY upload_at DESC
            ) as rn
            FROM date_range_parts
            WHERE DATE_TRUNC('month', start_date::date) = DATE_TRUNC('month', start_date::date) AND
              DATE_TRUNC('month', end_date::date) = DATE_TRUNC('month', end_date::date) AND
              start_date::date = DATE_TRUNC('month', start_date::date) AND
              end_date::date = (DATE_TRUNC('month', end_date::date) + INTERVAL '1 month' - INTERVAL '1 day')
          )
          SELECT *
          FROM ranked_reports
          WHERE rn = 1
          ORDER BY start_date DESC, upload_at DESC
        SQL
      when :daily
        <<-SQL
          WITH date_range_parts AS (
            SELECT
              *,
              CASE
                WHEN date_range LIKE '% to %' THEN
                  SPLIT_PART(date_range, ' to ', 1)
                ELSE
                  date_range
              END as start_date,
              CASE
                WHEN date_range LIKE '% to %' THEN
                  SPLIT_PART(date_range, ' to ', 2)
                ELSE
                  date_range
              END as end_date
            FROM client_daily_report_logs
            WHERE status = 1
              AND upload_path IS NOT null
              AND client_name IN ?
          ),
          ranked_reports AS (
            SELECT *,
            ROW_NUMBER() OVER (
              PARTITION BY client_name, date_range
              ORDER BY upload_at DESC
            ) as rn
            FROM date_range_parts
            WHERE start_date = end_date
          )
          SELECT *
          FROM ranked_reports
          WHERE rn = 1
          ORDER BY client_name, start_date DESC, upload_at DESC
        SQL
      when :rolling
        <<-SQL
          SELECT *
          FROM client_daily_report_logs
          WHERE status = 1
            AND upload_path IS NOT null
            AND client_name IN ?
          ORDER BY upload_at DESC
        SQL
      when :rolling_insert
        <<-SQL
          SELECT *
          FROM client_daily_report_logs
          WHERE status = 1
            AND upload_path IS NOT null
            AND client_name IN ?
          ORDER BY upload_at DESC
        SQL
      end
    end
  end
end
