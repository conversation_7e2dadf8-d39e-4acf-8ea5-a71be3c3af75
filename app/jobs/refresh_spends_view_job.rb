class RefreshSpendsViewJob < ApplicationJob
  queue_as :default

  include TaskTrackable

  def perform(start_date: 1.months.ago.beginning_of_month.to_date, end_date: Date.yesterday, user_id: nil)
    @start_date = start_date.to_date
    @end_date = end_date.to_date

    task_run.update!(parameters: { start_date: @start_date, end_date: @end_date })
    log_info "Refreshing campaign spends view for the period #{@start_date} to #{@end_date}"

    if user_id
      user = User.find(user_id)
      task_run.update!(user: user)
      log_info "Refreshing campaign spends view by user #{user.name}"
    else
      log_info "Refreshing campaign spends view by system"
    end

    log_info "Syncing client pricing ..."
    SyncExternal::ClientPricingsJob.perform_now

    log_info "Syncing vendor pricing ..."
    SyncExternal::PartnerPricingsJob.perform_now

    log_info "Importing spend data from external source (net spends) ..."
    SyncExternal::NetSpendsJob.perform_now(start_date: @start_date, end_date: @end_date)

    log_info "Syncing conversion events ..."
    SyncExternal::ConversionEventsJob.perform_now(start_date: @start_date, end_date: @end_date)

    log_info "Syncing overcap released conversion events ..."
    SyncExternal::OvercapReleasedConversionEventsJob.perform_now(start_date: @start_date, end_date: @end_date)

    enqueue_monthly_campaign_spend_adjustments(@start_date, @end_date)

    log_info "Generating campaign spends ..."
    GenerateCampaignSpendsJob.perform_now(start_date: @start_date, end_date: @end_date)

    log_info "Generating campaign spend adjustments ..."
    GenerateCampaignSpendAdjustmentsJob.perform_now(start_date: @start_date, end_date: @end_date)

    log_info "Refresh daily data view ..."
    RefreshCampaignSpendSummariesJob.perform_now

    log_info "Refresh monthly data view ..."
    RefreshMonthlyCampaignSpendSummariesJob.perform_now

    log_info "Refreshing daily campaign spends view ..."
    DailyCampaignSpend.refresh_data(@start_date)

    log_info "Refreshing monthly campaign spends view ..."
    MonthlyCampaignSpend.refresh_data(@start_date)

    log_info "Refreshing campaign spends view completed"
  end

  private

  def enqueue_monthly_campaign_spend_adjustments(start_date, end_date)
    current_month = start_date.beginning_of_month
    end_month = end_date.beginning_of_month

    while current_month <= end_month
      log_info "Syncing monthly campaign spend adjustments for #{current_month.strftime("%Y-%m")} ..."
      SyncExternal::MonthlyCampaignSpendAdjustmentsJob.perform_now(month: current_month.strftime("%Y-%m"))
      current_month = current_month.next_month
    end
  end
end
