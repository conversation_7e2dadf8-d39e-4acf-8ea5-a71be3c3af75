class SyncActiveStatusJob < ApplicationJob
  queue_as :default

  def perform(*args)
    sync_client_active_status
    sync_partner_active_status
  end

  private

  def sync_client_active_status
    # Get all clients that have campaign spend data since start of last month
    active_legacy_client_ids = campaign_spends_since_last_month
      .pluck(:legacy_client_id)
      .uniq
      .compact

    # Set clients as active if they have recent campaign spends
    Client.where(legacy_id: active_legacy_client_ids)
      .where.not(active: true)
      .update_all(active: true)

    # Set clients as inactive if they don't have recent campaign spends
    Client.where(active: true)
      .where.not(legacy_id: active_legacy_client_ids)
      .update_all(active: false)

    Rails.logger.info "Synced active status for #{active_legacy_client_ids.count} clients"
  end

  def sync_partner_active_status
    # Get all partners that have campaign spend data since start of last month
    active_legacy_partner_ids = campaign_spends_since_last_month
      .pluck(:legacy_partner_id)
      .uniq
      .compact

    # Set partners as running if they have recent campaign spends
    Partner.where(legacy_id: active_legacy_partner_ids)
      .where.not(status: "running")
      .update_all(status: "running")

    # Set partners as paused if they don't have recent campaign spends
    Partner.where(status: "running")
      .where.not(legacy_id: active_legacy_partner_ids)
      .update_all(status: "paused")

    Rails.logger.info "Synced active status for #{active_legacy_partner_ids.count} partners"
  end

  def campaign_spends_since_last_month
    @campaign_spends_since_last_month ||= DailyCampaignSpend
      .where("spend_date >= ?", Date.current.beginning_of_month.prev_month)
      .where(is_test: false)
  end
end
