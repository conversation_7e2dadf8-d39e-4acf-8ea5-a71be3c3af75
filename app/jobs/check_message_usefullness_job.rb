class CheckMessageUsefullnessJob < ApplicationJob
  queue_as :default

  def perform(*args)
    service = Bedrock::Service.new

    # Process GongCalls that have a client or partner
    GongCall.where.not(client_id: nil).or(GongCall.where.not(partner_id: nil)).find_each do |call|
      call.gong_transcripts.where(is_useful_checked_at: nil).find_each do |transcript|
        is_useful = service.check_message_usefullness(transcript.text)
        transcript.update(is_useful: is_useful, is_useful_checked_at: Time.current)
      end
    end

    # Process FirefliesMeetings that have a client or partner
    FirefliesMeeting.where.not(client_id: nil).or(FirefliesMeeting.where.not(partner_id: nil)).find_each do |meeting|
      meeting.fireflies_utterances.where(is_useful_checked_at: nil).find_each do |utterance|
        is_useful = service.check_message_usefullness(utterance.content)
        utterance.update(is_useful: is_useful, is_useful_checked_at: Time.current)
      end
    end
  end
end
