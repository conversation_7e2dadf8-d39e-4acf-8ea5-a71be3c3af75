class TrackMobileAppMetricsJob < ApplicationJob
  queue_as :default

  def perform(*args)
    mobile_apps = MobileApp.joins(:client).where(client: Client.active)

    if mobile_apps.empty?
      Rails.logger.info "No active mobile apps found to track"
      return
    end

    mobile_apps.find_in_batches(batch_size: 50) do |batch|
      begin
        track_batch(batch)
      rescue StandardError => e
        Rails.logger.error "Failed to track metrics batch: #{e.message}"
      end
    end
  end

  private

  def track_batch(mobile_apps)
    client = sensor_tower_client

    ios_apps = mobile_apps.select { |app| app.platform&.downcase == "ios" }
    android_apps = mobile_apps.select { |app| app.platform&.downcase == "android" }

    if ios_apps.any?
      process_platform_batch(client, ios_apps, "ios")
    end

    if android_apps.any?
      process_platform_batch(client, android_apps, "android")
    end
  end

  def process_platform_batch(client, apps, platform)
    bundle_ids = apps.map(&:bundle_id).compact.map(&:to_s)

    if bundle_ids.empty?
      Rails.logger.warn "No bundle IDs found for #{platform} apps"
      return
    end

    begin
      apps_data = case platform
      when "ios"
                    client.get_ios_apps(bundle_ids, api_options)
      when "android"
                    client.get_android_apps(bundle_ids, api_options)
      else
                    raise "Unsupported platform: #{platform}"
      end

      process_batch_response(apps, apps_data)

    rescue SensorTower::APIError => e
      Rails.logger.error "Sensor Tower API error for #{platform}: #{e.message}"
      fallback_individual_tracking(apps)
    end
  end

  def api_options
    {
      country: "US"
    }
  end

  def process_batch_response(apps, apps_data)
    apps.each do |app|
      begin
        bundle_id = app.bundle_id
        app_metrics = extract_app_data(apps_data, bundle_id)
        if app_metrics
          metric = app.mobile_app_metrics.find_or_initialize_by(metric_date: Date.current)

          update_attrs = {}
          update_attrs[:rating] = app_metrics["rating"] if app_metrics["rating"]
          update_attrs[:rating_count] = app_metrics["rating_count"] if app_metrics["rating_count"]
          update_attrs[:location] = app_metrics["location"] if app_metrics["location"]
          metric.update!(update_attrs)

          Rails.logger.info "Updated metrics for #{app.name} (#{bundle_id}): #{update_attrs.keys.join(', ')}"
        else
          Rails.logger.warn "No data found for #{app.name} (#{bundle_id})"
        end
      rescue => e
        Rails.logger.error "Failed to process metrics for #{app.name}: #{e.message}"
      end
    end
  end

  def extract_app_data(apps_data, bundle_id)
    return nil unless apps_data && apps_data["apps"]

    app_data = apps_data["apps"].find { |item| item["app_id"].to_s == bundle_id.to_s }
    unless app_data
      Rails.logger.error "No data found for app ID: #{bundle_id}"
      # raise "No data found for app ID: #{bundle_id}"
      return nil
    end

    {
      "rating" => app_data["rating"],
      "rating_count" => app_data["rating_count"],
      "location" => app_data["canonical_country"]
    }
  end

  def fallback_individual_tracking(mobile_apps)
    Rails.logger.info "Falling back to individual app tracking"

    mobile_apps.each do |mobile_app|
      begin
        track_individual_app(mobile_app)
      rescue StandardError => e
        Rails.logger.error "Failed to track individual app #{mobile_app.bundle_id}: #{e.message}"
      end
    end
  end

  def track_individual_app(mobile_app)
    client = sensor_tower_client
    platform = mobile_app.platform&.downcase

    apps_data = case platform
    when "ios"
                  client.get_ios_apps([ mobile_app.bundle_id ], api_options)
    when "android"
                  client.get_android_apps([ mobile_app.bundle_id ], api_options)
    else
                  Rails.logger.error "Unsupported platform for app #{mobile_app.bundle_id}: #{platform}"
                  return
    end

    process_batch_response([ mobile_app ], apps_data)
  end

  def sensor_tower_client
    SensorTower::Client.new
  end
end
