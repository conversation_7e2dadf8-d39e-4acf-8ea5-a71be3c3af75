class TrackMobileAppMetricsJob < ApplicationJob
  queue_as :default

  def perform(*args)
    MobileApp.joins(:client).where(client: Client.active).find_each do |mobile_app|
      begin
        track(mobile_app)
      rescue AppStoreParser::Error => e
        Rails.logger.error "Parser error for MobileApp ID #{mobile_app.id}: #{e.message}"
      rescue StandardError => e
        Rails.logger.error "Failed to track metrics for MobileApp ID #{mobile_app.id}: #{e.message}"
        Sentry.capture_exception(e, extra: { mobile_app_id: mobile_app.id })
      end
    end
  end

  private

  def track(mobile_app)
    data = AppStoreParser.parse(mobile_app.bundle_id, "en").to_hash

    # Validate required data was retrieved
    unless data[:current_rating] && data[:rating_count]
      Rails.logger.warn "No rating data found for MobileApp ID #{mobile_app.id}"
      return
    end

    # Extract region from the parser or default to 'us'
    location = extract_location_from_data(data) || "us"

    MobileAppMetric.find_or_initialize_by(
      mobile_app: mobile_app,
      metric_date: Date.current,
      location: location
    ).tap do |metric|
      metric.update!(
        rating: data[:current_rating]&.to_f,
        rating_count: data[:rating_count]&.to_i,
      )
    end
  end

  def extract_location_from_data(data)
    # This would need to be implemented based on how you want to determine location
    # Could be based on the app's target market, user preference, etc.
    "us"
  end
end
