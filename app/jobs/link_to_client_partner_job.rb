class LinkToClientPartnerJob < ApplicationJob
  queue_as :default

  def perform(*args)
    GongCall.where(client_id: nil, partner_id: nil)
            .where(association_checked_at: nil)
            .find_each do |call|
      next unless call.title.present?

      result = service.link_to_client_or_partner(call.title)

      if result
        updates = {}

        if result["linked_client"] && result["linked_client"] > 0
          updates[:client_id] = result["linked_client"] if Client.exists?(result["linked_client"])
        end

        if result["linked_partner"] && result["linked_partner"] > 0
          updates[:partner_id] = result["linked_partner"] if Partner.exists?(result["linked_partner"])
        end

        call.update!(updates) if updates.any?
      end

      call.update!(association_checked_at: Time.current)
    end

    FirefliesMeeting.where(client_id: nil, partner_id: nil)
                    .where(association_checked_at: nil)
                    .find_each do |meeting|
      next unless meeting.title.present?

      result = service.link_to_client_or_partner(meeting.title)

      if result
        updates = {}

        if result["linked_client"] && result["linked_client"] > 0
          updates[:client_id] = result["linked_client"] if Client.exists?(result["linked_client"])
        end

        if result["linked_partner"] && result["linked_partner"] > 0
          updates[:partner_id] = result["linked_partner"] if Partner.exists?(result["linked_partner"])
        end

        meeting.update!(updates) if updates.any?
      end

      meeting.update!(association_checked_at: Time.current)
    end
  end

  private

  def service
    @service ||= Bedrock::Service.new
  end
end
