class VerifyData::AllSpendJob < ApplicationJob
  queue_as :default
  include TaskTrackable

  class SpendMismatchError < StandardError; end

  SPEND_TYPES = [ "client", "partner" ].freeze

  def perform(start_date: 1.months.ago.beginning_of_month.to_date, end_date: Date.yesterday - 1.day)
    @start_date = start_date.to_date
    @end_date = end_date.to_date

    task_run.update!(parameters: { start_date: @start_date, end_date: @end_date })

    log_info "Verifying all spend for the period #{@start_date} to #{@end_date}"
    log_info "Comparing with external spend data, sourced from https://admin.feedmob.com/spends"

    SPEND_TYPES.each do |spend_type|
      verify_all_entities_spend(spend_type)
    end
  end

  private

  def verify_all_entities_spend(spend_type)
    entities = spend_type == "client" ? clients : partners
    entities.each { |entity| verify_entity_spend(entity, spend_type) }
  end

  def verify_entity_spend(entity, spend_type)
    if entity.is_test
      log_info "Skipping spend verification for test #{spend_type}: #{entity.name}"
      return
    end

    total_summary_spend = fetch_total_summary_spend(entity, spend_type)
    total_external_spend = normalize_cents_to_dollars(external_gross_spend(entity, spend_type))

    if spend_matches?(total_summary_spend, total_external_spend)
      log_info "Pass: Total spend matches for #{spend_type} #{entity.name}"
      return
    end

    log_error "Total spend mismatch for #{spend_type} #{entity.name}:" \
              " summary_spend=#{total_summary_spend}," \
              " external_spend=#{total_external_spend}," \
              " difference=#{(total_summary_spend - total_external_spend).abs}"

    verify_daily_spend(entity, spend_type)
  end

  def verify_daily_spend(entity, spend_type)
    @start_date.upto(@end_date) do |date|
      verify_entity_spend_for_date(entity, date, spend_type)
    end
  end

  def verify_entity_spend_for_date(entity, date, spend_type)
    daily_summary_spend = fetch_total_summary_spend(entity, spend_type, start_date: date, end_date: date)
    daily_external_spend = normalize_cents_to_dollars(external_gross_spend(entity, spend_type, start_date: date, end_date: date))

    if spend_matches?(daily_summary_spend, daily_external_spend)
      log_info "Pass: Daily spend matches for #{spend_type} #{entity.name} on #{date}"
      return
    end

    log_error "Daily spend mismatch for #{spend_type} #{entity.name} on #{date}:" \
              " summary_spend=#{daily_summary_spend}," \
              " external_spend=#{daily_external_spend}," \
              " difference=#{(daily_summary_spend - daily_external_spend).abs}"

    click_url_ids = legacy_click_url_ids_by_date_and_entity(date, entity, spend_type)

    click_url_ids.each do |legacy_click_url_id|
      verify_click_url_spend(legacy_click_url_id, date, spend_type)
    end
  end

  def verify_click_url_spend(legacy_click_url_id, date, spend_type)
    summary = CampaignSpendSummary.find_by(
      spend_date: date,
      legacy_click_url_id: legacy_click_url_id
    )

    external_amount = normalize_cents_to_dollars(
      fetch_external_spend(legacy_click_url_id, date, spend_type)
    )

    if summary.nil? && external_amount.nil?
      return
    end

    if summary.nil? && external_amount > 0
      click_url = ClickUrl.find_by(legacy_id: legacy_click_url_id)
      return if skip_test_click_url?(click_url)

      raise SpendMismatchError, "Click URL spend mismatch: click_url_id=#{legacy_click_url_id}, date=#{date}, external_amount=#{external_amount}, spend_source=external"
    end

    if summary.nil?
      # External spend is 0 or nil, and there is no summary record
      return
    end

    if summary.is_test
      log_info "Skipping spend verification for test click_url (id: #{legacy_click_url_id})"
      return
    end

    campaign_amount = spend_type == "client" ? summary.gross_spend : summary.net_spend

    if spend_matches?(campaign_amount, external_amount)
      return
    end

    difference = (campaign_amount - external_amount).abs
    percentage_difference = ((difference / external_amount) * 100).round(2)

    error_message = build_error_message(summary, campaign_amount, external_amount, difference, percentage_difference, spend_type)
    raise SpendMismatchError, error_message
  end

  def fetch_total_summary_spend(entity, spend_type, start_date: @start_date, end_date: @end_date)
    query = CampaignSpendSummary
      .where("spend_date >= ? AND spend_date <= ?", start_date, end_date)

    if spend_type == "client"
      query = query.where(legacy_client_id: entity.legacy_id)
      query.sum(:gross_spend) || 0.0
    else
      query = query.where(legacy_partner_id: entity.legacy_id)
      query.sum(:net_spend) || 0.0
    end
  end

  def external_gross_spend(entity, spend_type, start_date: @start_date, end_date: @end_date)
    query = ExternalDatabase.main[:direct_spends]
      .where(spend_date: start_date..end_date)

    if spend_type == "client"
      query = query.where(client_id: entity.legacy_id)
      query.sum(:gross_spend_cents)
    else
      query = query.where(vendor_id: entity.legacy_id)
      query.sum(:net_spend_cents)
    end
  end

  def clients
    legacy_client_ids = CampaignSpendSummary.where(spend_date: @start_date..@end_date).pluck(:legacy_client_id)
    Client.where(legacy_id: legacy_client_ids)
  end

  def partners
    legacy_partner_ids = CampaignSpendSummary.where(spend_date: @start_date..@end_date).pluck(:legacy_partner_id)
    Partner.where(legacy_id: legacy_partner_ids)
  end

    private

    def spend_matches?(amount1, amount2)
      amount1 == amount2
    end

    def normalize_cents_to_dollars(cents)
      return 0.0 if cents.nil?
      cents / 100.0
    end

    def fetch_external_spend(legacy_click_url_id, date, spend_type)
      spend = ExternalDatabase.main[:direct_spends]
        .where(spend_date: date, click_url_id: legacy_click_url_id)
        .first

      return nil unless spend
      spend_type == "client" ? spend[:gross_spend_cents] : spend[:net_spend_cents]
    end

    def legacy_click_url_ids_by_date_and_entity(date, entity, spend_type)
      query = ExternalDatabase.main[:direct_spends]
        .where(spend_date: date)

      if spend_type == "client"
        query = query.where(client_id: entity.legacy_id)
      else
        query = query.where(vendor_id: entity.legacy_id)
      end

      query.select(:click_url_id)
        .distinct
        .pluck(:click_url_id)
    end

    def skip_test_click_url?(click_url)
      return false unless click_url

      if click_url.partner.is_test
        log_info "Skipping spend verification for test click_url because of vendor"
        return true
      end

      if click_url.client.is_test
        log_info "Skipping spend verification for test click_url because of client"
        return true
      end

      false
    end

    def build_error_message(summary, campaign_amount, external_amount, difference, percentage_difference, spend_type)
      base_message = "Click URL spend mismatch:" \
        " click_url_id=#{summary.legacy_click_url_id}," \
        " date=#{summary.spend_date}," \
        " #{spend_type}_amount=#{campaign_amount}," \
        " external_amount=#{external_amount}," \
        " absolute_difference=#{difference}," \
        " percentage_difference=#{percentage_difference}%," \
        " spend_source=#{spend_type == 'client' ? summary.gross_spend_source : summary.net_spend_source}," \
        " campaign_id=#{summary.legacy_campaign_id}," \
        " client_id=#{summary.legacy_client_id}," \
        " partner_id=#{summary.legacy_partner_id}"

      spend_source = spend_type == "client" ? summary.gross_spend_source : summary.net_spend_source

      if spend_source == "event_aggregate"
        metadata = summary.calculation_metadata
        base_message + "," \
          " event=#{metadata[:event]}," \
          " count=#{metadata[:count]}," \
          " rate=#{metadata[:rate]}," \
          " data_origin=#{metadata[:data_origin]}," \
          " pricing_model=#{metadata[:pricing_model]}"
      else
        base_message
      end
    end
end
