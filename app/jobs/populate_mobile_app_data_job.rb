# app/jobs/populate_mobile_app_data_job.rb
class PopulateMobileAppDataJob < ApplicationJob
  queue_as :default

  class AppStoreParsingError < StandardError; end

  def perform(mobile_app_id = nil)
    if mobile_app_id
      process_single_app(mobile_app_id)
    else
      process_all_apps
    end
  end

  private

  def process_all_apps
    # Process iOS apps first
    MobileApp.where(platform: "ios").find_each(batch_size: 50) do |app|
      process_single_app(app.id)
    rescue => e
      Rails.logger.error "Failed to process iOS app #{app.id}: #{e.message}"
      # Continue processing other apps
    end

    # Then process Android apps
    MobileApp.where(platform: "android").find_each(batch_size: 50) do |app|
      process_single_app(app.id)
    rescue => e
      Rails.logger.error "Failed to process Android app #{app.id}: #{e.message}"
      # Continue processing other apps
    end
  end

  def process_single_app(mobile_app_id)
    mobile_app = MobileApp.find(mobile_app_id)
    Rails.logger.info "Processing mobile app: #{mobile_app.name} (ID: #{mobile_app.id})"

    # Process based on the app's actual platform
    if ios_app?(mobile_app)
      process_ios_app(mobile_app)
      Rails.logger.info "Successfully processed iOS app: #{mobile_app.name}"
    elsif android_app?(mobile_app)
      # Find iOS counterpart for category data
      ios_data = find_ios_data_for_android_app(mobile_app)
      process_android_app(mobile_app, ios_data)
      Rails.logger.info "Successfully processed Android app: #{mobile_app.name}"
    else
      Rails.logger.warn "Unknown platform for app: #{mobile_app.name} (#{mobile_app.platform})"
      return
    end

    Rails.logger.info "Successfully processed mobile app: #{mobile_app.name}"
  rescue => e
    Rails.logger.error "Failed to process mobile app #{mobile_app_id}: #{e.message}"
    raise
  end

  def process_ios_app(mobile_app)
    # Skip if no bundle_id or not an iOS app
    return nil unless mobile_app.bundle_id.present? && ios_app?(mobile_app)

    Rails.logger.info "Processing iOS app: #{mobile_app.name}"

    begin
      parser_data = AppStoreParser.parse(mobile_app.bundle_id, "en")
      update_app_with_parser_data(mobile_app, parser_data, "ios")

      {
        primary_category: parser_data.primary_genre,
        all_categories: parser_data.genre_names,
        parser_data: parser_data
      }
    rescue AppStoreParser::Error => e
      Rails.logger.warn "iOS parsing failed for #{mobile_app.name}: #{e.message}"
      nil
    end
  end

  def process_android_app(mobile_app, ios_data = nil)
    # For Android apps, we need to construct the Google Play URL from bundle_id
    return nil unless mobile_app.bundle_id.present? && android_app?(mobile_app)

    Rails.logger.info "Processing Android app: #{mobile_app.name}"

    begin
      # Use bundle_id to construct Google Play URL
      parser_data = AppStoreParser.parse(mobile_app.bundle_id, "en")

      # For Android, use iOS categories if available, otherwise use parsed categories
      if ios_data&.dig(:all_categories)&.any?
        Rails.logger.info "Using iOS categories for Android app: #{mobile_app.name}"
        # Override Android categories with iOS ones
        parser_data.instance_variable_set(:@genre_names, ios_data[:all_categories])
        parser_data.instance_variable_set(:@primary_genre, ios_data[:primary_category])

        # Update app data including categories
        update_app_with_parser_data(mobile_app, parser_data, "android")
      else
        Rails.logger.info "No iOS category data available for Android app: #{mobile_app.name}, skipping category updates"
        # Update app data but skip categories
        update_app_without_categories(mobile_app, parser_data, "android")
      end

      {
        primary_category: parser_data.primary_genre,
        all_categories: parser_data.genre_names,
        parser_data: parser_data
      }
    rescue AppStoreParser::Error => e
      Rails.logger.warn "Android parsing failed for #{mobile_app.name}: #{e.message}"
      nil
    end
  end

  def update_app_with_parser_data(mobile_app, parser_data, platform_context)
    ActiveRecord::Base.transaction do
      # Update main category
      update_main_category(mobile_app, parser_data.primary_genre)

      # Update categorizations (all categories)
      update_categorizations(mobile_app, parser_data.genre_names)

      # Update logo
      update_logo(mobile_app, parser_data.artwork) if parser_data.artwork.present?

      # Update basic app info if missing
      update_basic_info(mobile_app, parser_data)
    end
  end

  def update_app_without_categories(mobile_app, parser_data, platform_context)
    ActiveRecord::Base.transaction do
      # Update logo
      update_logo(mobile_app, parser_data.artwork) if parser_data.artwork.present?

      # Update basic app info if missing
      update_basic_info(mobile_app, parser_data)
    end
  end

  def update_main_category(mobile_app, primary_genre)
    return if primary_genre.blank?

    category = MobileAppCategory.find_or_create_by_name(primary_genre)
    if category && mobile_app.main_category != category
      mobile_app.update!(main_category: category)
      Rails.logger.info "Updated main category for #{mobile_app.name}: #{category.name}"
    end
  end

  def update_categorizations(mobile_app, genre_names)
    return if genre_names.blank?

    # Get or create categories
    categories = genre_names.filter_map do |genre_name|
      MobileAppCategory.find_or_create_by_name(genre_name)
    end

    # Get current categorizations
    current_categories = mobile_app.mobile_app_categories.to_a

    # Add new categorizations
    categories.each do |category|
      unless current_categories.include?(category)
        mobile_app.mobile_app_categorizations.create!(mobile_app_category: category)
        Rails.logger.info "Added categorization for #{mobile_app.name}: #{category.name}"
      end
    end

    # Remove categories that are no longer present
    categories_to_remove = current_categories - categories
    categories_to_remove.each do |category|
      mobile_app.mobile_app_categorizations.find_by(mobile_app_category: category)&.destroy
    end
  end

  def update_logo(mobile_app, artwork_url)
    return if mobile_app.logo.attached?

    begin
      # Download and attach the logo
      uri = URI.parse(artwork_url)
      downloaded_file = uri.open

      filename = "#{mobile_app.name.parameterize}_logo#{File.extname(uri.path)}"
      filename = "logo.png" if filename.blank? || filename == "_logo"

      mobile_app.logo.attach(
        io: downloaded_file,
        filename: filename,
        content_type: downloaded_file.content_type
      )

      Rails.logger.info "Updated logo for #{mobile_app.name}"
    rescue => e
      Rails.logger.warn "Failed to download logo for #{mobile_app.name}: #{e.message}"
    end
  end

  def update_basic_info(mobile_app, parser_data)
    updates = {}

    # Update description if missing
    if mobile_app.description.blank? && parser_data.description.present?
      updates[:description] = parser_data.description
    end

    # Update store_url if missing
    if mobile_app.store_url.blank? && parser_data.url.present?
      updates[:store_url] = parser_data.url
    end

    if updates.any?
      mobile_app.update!(updates)
      Rails.logger.info "Updated basic info for #{mobile_app.name}: #{updates.keys.join(', ')}"
    end
  end

  def ios_app?(mobile_app)
    mobile_app.platform == "ios"
  end

  def android_app?(mobile_app)
    mobile_app.platform == "android"
  end

  def find_ios_data_for_android_app(android_app)
    # Find iOS app with the same name
    ios_app = MobileApp.where(name: android_app.name)
                      .find { |app| ios_app?(app) }

    return nil unless ios_app

    Rails.logger.info "Found iOS counterpart for Android app: #{android_app.name}"

    # If iOS app has categories, return them
    if ios_app.mobile_app_categories.any?
      {
        primary_category: ios_app.main_category&.name,
        all_categories: ios_app.mobile_app_categories.pluck(:name)
      }
    else
      # If no categories, try to process the iOS app to get fresh data
      Rails.logger.info "iOS counterpart has no categories, processing iOS app first"
      process_ios_app(ios_app)
    end
  rescue => e
    Rails.logger.warn "Failed to find iOS data for Android app #{android_app.name}: #{e.message}"
    nil
  end
end
