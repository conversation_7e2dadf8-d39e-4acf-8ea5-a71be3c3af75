# app/jobs/populate_mobile_app_data_job.rb
class PopulateMobileAppDataJob < ApplicationJob
  queue_as :default

  class AppDataError < StandardError; end

  def perform(mobile_app_id = nil)
    if mobile_app_id
      process_single_app(mobile_app_id)
    else
      process_all_apps
    end
  end

  private

  def process_all_apps
    # Process iOS apps in batches
    ios_apps = MobileApp.where(platform: "ios").where.not(bundle_id: [ nil, "" ])
    process_apps_in_batches(ios_apps, "ios")

    # Process Android apps in batches
    android_apps = MobileApp.where(platform: "android").where.not(bundle_id: [ nil, "" ])
    process_apps_in_batches(android_apps, "android")
  end

  def process_apps_in_batches(apps, platform)
    apps.find_in_batches(batch_size: 20) do |batch|
      begin
        process_batch(batch, platform)
      rescue => e
        Rails.logger.error "Failed to process #{platform} batch: #{e.message}"
        # Fallback to individual processing
        batch.each { |app| process_single_app(app.id) }
      end
    end
  end

  def process_batch(apps, platform)
    bundle_ids = apps.map(&:bundle_id).compact
    return if bundle_ids.empty?

    Rails.logger.info "Processing #{platform} batch with #{bundle_ids.size} apps"

    begin
      # Get data for all apps in one API call
      sensor_tower_data = case platform
      when "ios"
        sensor_tower_client.get_ios_apps(bundle_ids, api_options)
      when "android"
        sensor_tower_client.get_android_apps(bundle_ids, api_options)
      else
        raise "Unsupported platform: #{platform}"
      end

      # Process each app with the batch data
      apps.each do |app|
        begin
          process_app_with_batch_data(app, sensor_tower_data, platform)
        rescue => e
          Rails.logger.error "Failed to process app #{app.name} in batch: #{e.message}"
        end
      end

    rescue SensorTower::Client::APIError => e
      Rails.logger.error "SensorTower API error for #{platform} batch: #{e.message}"
      raise
    end
  end

  def process_app_with_batch_data(mobile_app, sensor_tower_data, platform)
    Rails.logger.info "Processing #{platform} app: #{mobile_app.name}"

    begin
      app_data = SensorTower::AppDataAdapter.new(sensor_tower_data, mobile_app.bundle_id, platform)

      # For Android apps, try to find iOS counterpart for categories
      if platform == "android"
        ios_data = find_ios_data_for_android_app(mobile_app)
        if ios_data&.dig(:all_categories)&.any?
          Rails.logger.info "Using iOS categories for Android app: #{mobile_app.name}"
          app_data.instance_variable_set(:@genre_names, ios_data[:all_categories])
          app_data.instance_variable_set(:@primary_genre, ios_data[:primary_category])
        end
      end

      update_app_with_app_data(mobile_app, app_data, platform)
      Rails.logger.info "Successfully processed #{platform} app: #{mobile_app.name}"

    rescue SensorTower::AppDataAdapter::AppNotFoundError => e
      Rails.logger.warn "App data not found for #{mobile_app.name}: #{e.message}"
    end
  end

  def process_single_app(mobile_app_id)
    mobile_app = MobileApp.find(mobile_app_id)
    Rails.logger.info "Processing mobile app: #{mobile_app.name} (ID: #{mobile_app.id})"

    # Process based on the app's actual platform
    if ios_app?(mobile_app)
      process_ios_app(mobile_app)
      Rails.logger.info "Successfully processed iOS app: #{mobile_app.name}"
    elsif android_app?(mobile_app)
      # Find iOS counterpart for category data
      ios_data = find_ios_data_for_android_app(mobile_app)
      process_android_app(mobile_app, ios_data)
      Rails.logger.info "Successfully processed Android app: #{mobile_app.name}"
    else
      Rails.logger.warn "Unknown platform for app: #{mobile_app.name} (#{mobile_app.platform})"
      return
    end

    Rails.logger.info "Successfully processed mobile app: #{mobile_app.name}"
  rescue => e
    Rails.logger.error "Failed to process mobile app #{mobile_app_id}: #{e.message}"
    raise
  end

  def process_ios_app(mobile_app)
    # Skip if no bundle_id or not an iOS app
    return nil unless mobile_app.bundle_id.present? && ios_app?(mobile_app)

    Rails.logger.info "Processing iOS app: #{mobile_app.name}"

    begin
      sensor_tower_data = sensor_tower_client.get_ios_apps([ mobile_app.bundle_id ], api_options)

      # Check if we got valid data
      unless sensor_tower_data.is_a?(Hash) && sensor_tower_data["apps"].is_a?(Array)
        Rails.logger.warn "Invalid response from SensorTower for iOS app #{mobile_app.name}"
        return nil
      end

      app_data = SensorTower::AppDataAdapter.new(sensor_tower_data, mobile_app.bundle_id, "ios")
      update_app_with_app_data(mobile_app, app_data, "ios")

      {
        primary_category: app_data.primary_genre,
        all_categories: app_data.genre_names,
        app_data: app_data
      }
    rescue SensorTower::Client::APIError, SensorTower::AppDataAdapter::AppNotFoundError => e
      Rails.logger.warn "iOS data fetching failed for #{mobile_app.name}: #{e.message}"
      nil
    end
  end

  def process_android_app(mobile_app, ios_data = nil)
    # For Android apps, we need to use bundle_id to get data from SensorTower
    return nil unless mobile_app.bundle_id.present? && android_app?(mobile_app)

    Rails.logger.info "Processing Android app: #{mobile_app.name}"

    begin
      sensor_tower_data = sensor_tower_client.get_android_apps([ mobile_app.bundle_id ], api_options)

      # Check if we got valid data
      unless sensor_tower_data.is_a?(Hash) && sensor_tower_data["apps"].is_a?(Array)
        Rails.logger.warn "Invalid response from SensorTower for Android app #{mobile_app.name}"
        return nil
      end

      app_data = SensorTower::AppDataAdapter.new(sensor_tower_data, mobile_app.bundle_id, "android")

      # For Android, use iOS categories if available, otherwise use SensorTower categories
      if ios_data&.dig(:all_categories)&.any?
        Rails.logger.info "Using iOS categories for Android app: #{mobile_app.name}"
        # Override Android categories with iOS ones
        app_data.instance_variable_set(:@genre_names, ios_data[:all_categories])
        app_data.instance_variable_set(:@primary_genre, ios_data[:primary_category])

        # Update app data including categories
        update_app_with_app_data(mobile_app, app_data, "android")
      else
        Rails.logger.info "No iOS category data available for Android app: #{mobile_app.name}, using SensorTower categories"
        # Update app data with SensorTower categories
        update_app_with_app_data(mobile_app, app_data, "android")
      end

      {
        primary_category: app_data.primary_genre,
        all_categories: app_data.genre_names,
        app_data: app_data
      }
    rescue SensorTower::Client::APIError, SensorTower::AppDataAdapter::AppNotFoundError => e
      Rails.logger.warn "Android data fetching failed for #{mobile_app.name}: #{e.message}"
      nil
    end
  end

  def update_app_with_app_data(mobile_app, app_data, platform_context)
    ActiveRecord::Base.transaction do
      # Update main category
      update_main_category(mobile_app, app_data.primary_genre)

      # Update categorizations (all categories)
      update_categorizations(mobile_app, app_data.genre_names)

      # Update logo
      update_logo(mobile_app, app_data.artwork) if app_data.artwork.present?

      # Update basic app info if missing
      update_basic_info(mobile_app, app_data)
    end
  end

  def update_app_without_categories(mobile_app, app_data, platform_context)
    ActiveRecord::Base.transaction do
      # Update logo
      update_logo(mobile_app, app_data.artwork) if app_data.artwork.present?

      # Update basic app info if missing
      update_basic_info(mobile_app, app_data)
    end
  end

  def update_main_category(mobile_app, primary_genre)
    return if primary_genre.blank?

    category = MobileAppCategory.find_or_create_by_name(primary_genre)
    if category && mobile_app.main_category != category
      mobile_app.update!(main_category: category)
      Rails.logger.info "Updated main category for #{mobile_app.name}: #{category.name}"
    end
  end

  def update_categorizations(mobile_app, genre_names)
    return if genre_names.blank?

    # Get or create categories
    categories = genre_names.filter_map do |genre_name|
      MobileAppCategory.find_or_create_by_name(genre_name)
    end

    # Get current categorizations
    current_categories = mobile_app.mobile_app_categories.to_a

    # Add new categorizations
    categories.each do |category|
      unless current_categories.include?(category)
        mobile_app.mobile_app_categorizations.create!(mobile_app_category: category)
        Rails.logger.info "Added categorization for #{mobile_app.name}: #{category.name}"
      end
    end

    # Remove categories that are no longer present
    categories_to_remove = current_categories - categories
    categories_to_remove.each do |category|
      mobile_app.mobile_app_categorizations.find_by(mobile_app_category: category)&.destroy
    end
  end

  def update_logo(mobile_app, artwork_url)
    return if mobile_app.logo.attached?

    begin
      # Download and attach the logo
      uri = URI.parse(artwork_url)
      downloaded_file = uri.open

      filename = "#{mobile_app.name.parameterize}_logo#{File.extname(uri.path)}"
      filename = "logo.png" if filename.blank? || filename == "_logo"

      mobile_app.logo.attach(
        io: downloaded_file,
        filename: filename,
        content_type: downloaded_file.content_type
      )

      Rails.logger.info "Updated logo for #{mobile_app.name}"
    rescue => e
      Rails.logger.warn "Failed to download logo for #{mobile_app.name}: #{e.message}"
    end
  end

  def update_basic_info(mobile_app, app_data)
    updates = {}

    # Update description if missing
    if mobile_app.description.blank? && app_data.description.present?
      updates[:description] = app_data.description
    end

    # Update store_url if missing
    if mobile_app.store_url.blank? && app_data.url.present?
      updates[:store_url] = app_data.url
    end

    if updates.any?
      mobile_app.update!(updates)
      Rails.logger.info "Updated basic info for #{mobile_app.name}: #{updates.keys.join(', ')}"
    end
  end

  def ios_app?(mobile_app)
    mobile_app.platform == "ios"
  end

  def android_app?(mobile_app)
    mobile_app.platform == "android"
  end

  def find_ios_data_for_android_app(android_app)
    # Find iOS app with the same name
    ios_app = MobileApp.where(name: android_app.name)
                      .find { |app| ios_app?(app) }

    return nil unless ios_app

    Rails.logger.info "Found iOS counterpart for Android app: #{android_app.name}"

    # If iOS app has categories, return them
    if ios_app.mobile_app_categories.any?
      {
        primary_category: ios_app.main_category&.name,
        all_categories: ios_app.mobile_app_categories.pluck(:name)
      }
    else
      # If no categories, try to process the iOS app to get fresh data
      Rails.logger.info "iOS counterpart has no categories, processing iOS app first"
      process_ios_app(ios_app)
    end
  rescue => e
    Rails.logger.warn "Failed to find iOS data for Android app #{android_app.name}: #{e.message}"
    nil
  end

  def sensor_tower_client
    @sensor_tower_client ||= SensorTower::Client.new
  end

  def api_options
    {
      country: "US"
    }
  end
end
