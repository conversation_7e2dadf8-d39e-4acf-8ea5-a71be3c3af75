module MonthlyCampaignSpendsHelper
  def monthly_spend_tooltip_content(campaign_spend, spend_type)
    return unless campaign_spend

    if spend_type == "client"
      render partial: "monthly_campaign_spends/gross_tooltip", locals: { campaign_spend: campaign_spend }
    else
      render partial: "monthly_campaign_spends/net_tooltip", locals: { campaign_spend: campaign_spend }
    end
  end
  def campaign_spends_redirect_parameters(legacy_campaign_id, legacy_partner_id, month_date)
    params = request.query_parameters

    filtered_params = {
      query: {
        hide_test_data: params.dig(:query, :hide_test_data)
      }
    }

    # Handle legacy IDs with deduplication
    legacy_partner_ids = [
      params.dig(:query, :legacy_partner_id_in),
      legacy_partner_id
    ].compact.flatten.uniq
    filtered_params[:query][:legacy_partner_id_in] = legacy_partner_ids if legacy_partner_ids.present?

    legacy_campaign_ids = [
      params.dig(:query, :legacy_campaign_id_in),
      legacy_campaign_id
    ].compact.flatten.uniq
    filtered_params[:query][:legacy_campaign_id_in] = legacy_campaign_ids if legacy_campaign_ids.present?

    # Handle legacy client IDs
    legacy_client_ids = params.dig(:query, :legacy_client_id_in)
    filtered_params[:query][:legacy_client_id_in] = legacy_client_ids if legacy_client_ids.present?

    # Handle date transformations using month_date parameter
    if month_date.present?
      begin
        date = Date.parse(month_date.to_s + "-01")
        filtered_params[:query][:date_gteq] = date.beginning_of_month
        filtered_params[:query][:date_lteq] = date.end_of_month
      rescue ArgumentError => e
        Rails.logger.error("Invalid month_date format: #{month_date}, error: #{e.message}")
      end
    end

    # Remove nil values from the nested query hash
    filtered_params[:query].compact!
    filtered_params
  end
end
