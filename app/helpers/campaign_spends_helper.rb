module CampaignSpendsHelper
  def format_spend_amount(amount, campaign_spend = nil)
    return "-" if amount.nil?

    if campaign_spend.present?
      metadata = campaign_spend.calculation_metadata
      return "No Data" if amount.to_f.zero? && metadata.present? && metadata["spend_source"] == "partner"
    end

    formatted_amount = number_to_currency(amount)
    dollars, cents = formatted_amount.split(".")

    capture do
      concat content_tag(:span, dollars, class: "font-medium")
      concat content_tag(:span, ".#{cents}", class: "text-xs text-gray-400 dark:text-gray-500")
    end
  end

  def spend_tooltip_content(campaign_spend)
    return unless campaign_spend

    source = campaign_spend.calculation_source
    metadata = campaign_spend.calculation_metadata
    adjustments = campaign_spend.campaign_spend_adjustments

    case source
    when "event_aggregate"
      render partial: "campaign_spends/event_aggregate_tooltip", locals: { metadata: metadata, adjustments: adjustments }
    when "imported"
      render partial: "campaign_spends/imported_tooltip", locals: { metadata: metadata }
    end
  end

  def reverse_event_mapping(event_constant)
    reverse_mapping = {
      PaidEvent::CLICK => 1,
      PaidEvent::INSTALL => 2,
      PaidEvent::RETAINED => 3,
      PaidEvent::TUTORIAL => 4,
      PaidEvent::REGISTRATION => 5,
      PaidEvent::CONVERSION => 6,
      PaidEvent::PURCHASE => "first_purchase",
      PaidEvent::LEVEL => 8,
      PaidEvent::OPEN => 9,
      PaidEvent::ALL_EVENT_A => 100,
      PaidEvent::FIRST_EVENT_A => 101,
      PaidEvent::ALL_EVENT_B => 102,
      PaidEvent::FIRST_EVENT_B => 103
    }
    reverse_mapping[event_constant] || raise("Unrecognized event constant: #{event_constant}")
  end

  def conversion_records_link(start_date, end_date, legacy_click_url_id, track_type)
    date_range = "#{start_date}+-+#{end_date}"
    "https://admin.feedmob.com/conversion_records/es_search?date_range=#{date_range}&click_id=#{legacy_click_url_id}&status=1&track_type=#{reverse_event_mapping(track_type)}"
  end
end
