require "js"
require "json"

def request(url, method: "GET", headers: {}, params: {}, body: nil)
  response = JS.global.fetch("/proxy", {
    method: "POST",
    headers: {
      "Content-Type" => "application/json"
    },
    body: JS.global[:JSON].stringify({
      url: url,
      method: method,
      headers: headers,
      params: params,
      body: body,
    })
  }).await

  unless response[:ok]
    puts "Request failed with status: #{response.status}"
    return nil
  end

  response_text = response.text.await.to_s
  begin
    parsed_response = JSON.parse(response_text)
    return parsed_response
  rescue
    return response_text
  end
end

$stdout = Object.new.tap do |obj|
  def obj.write(str)
    JS.global[:document].querySelector('[data-ruby-wasm-target="output"]').insertAdjacentHTML("beforeend", str)
  end
end

$stderr = Object.new.tap do |obj|
  def obj.write(str)
    JS.global[:document].querySelector('[data-ruby-wasm-target="output"]').insertAdjacentHTML("beforeend", "<span class='text-red-400'>" + str + "</span>")
  end
end