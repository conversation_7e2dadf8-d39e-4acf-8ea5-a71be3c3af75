#!/usr/bin/env ruby

# Test script to verify SensorTower migration
require_relative 'config/environment'

puts "Testing SensorTower::Client migration..."

# Test SensorTower::AppDataAdapter
puts "\n1. Testing SensorTower::AppDataAdapter..."

sample_data = {
  "apps" => [
    {
      "app_id" => "284910350",
      "name" => "Test iOS App",
      "icon_url" => "https://example.com/icon.png",
      "description" => "Test description",
      "rating" => 4.5,
      "rating_count" => 1000,
      "categories" => [ "Games", "Action" ],
      "store_url" => "https://apps.apple.com/app/id284910350",
      "publisher" => "Test Publisher"
    }
  ]
}

# Test with nil data (error case)
puts "\n1.1 Testing with nil data..."
begin
  adapter = SensorTower::AppDataAdapter.new(nil, "284910350", "ios")
  puts "✗ Should have raised AppNotFoundError"
rescue SensorTower::AppDataAdapter::AppNotFoundError => e
  puts "✓ Correctly handled nil data: #{e.message}"
end

# Test with empty apps array
puts "\n1.2 Testing with empty apps array..."
begin
  adapter = SensorTower::AppDataAdapter.new({ "apps" => [] }, "284910350", "ios")
  puts "✗ Should have raised AppNotFoundError"
rescue SensorTower::AppDataAdapter::AppNotFoundError => e
  puts "✓ Correctly handled empty apps array: #{e.message}"
end

begin
  adapter = SensorTower::AppDataAdapter.new(sample_data, "284910350", "ios")
  puts "✓ AppDataAdapter created successfully"
  puts "  - App ID: #{adapter.app_id}"
  puts "  - Name: #{adapter.name}"
  puts "  - Categories: #{adapter.genre_names}"
  puts "  - Primary Category: #{adapter.primary_genre}"
  puts "  - Store Type: #{adapter.store_type}"
rescue => e
  puts "✗ AppDataAdapter failed: #{e.message}"
end

# Test PopulateMobileAppDataJob methods
puts "\n2. Testing PopulateMobileAppDataJob methods..."

job = PopulateMobileAppDataJob.new

# Test sensor_tower_client method
begin
  client = job.send(:sensor_tower_client)
  puts "✓ SensorTower client created successfully"
  puts "  - Client class: #{client.class}"
rescue => e
  puts "✗ SensorTower client creation failed: #{e.message}"
end

# Test api_options method
begin
  options = job.send(:api_options)
  puts "✓ API options retrieved successfully"
  puts "  - Options: #{options}"
rescue => e
  puts "✗ API options retrieval failed: #{e.message}"
end

# Test batch processing logic
puts "\n3. Testing batch processing logic..."

job = PopulateMobileAppDataJob.new

# Test batch size and filtering
ios_apps = MobileApp.where(platform: "ios").where.not(bundle_id: [ nil, "" ])
android_apps = MobileApp.where(platform: "android").where.not(bundle_id: [ nil, "" ])

puts "Found #{ios_apps.count} iOS apps with bundle IDs"
puts "Found #{android_apps.count} Android apps with bundle IDs"

# Test with a real mobile app if available
puts "\n4. Testing with real mobile app data..."

mobile_app = MobileApp.first
if mobile_app
  puts "Found mobile app: #{mobile_app.name} (#{mobile_app.platform})"
  puts "Bundle ID: #{mobile_app.bundle_id}"

  # Note: This would make a real API call, so we'll skip it in testing
  puts "Skipping real API call to avoid rate limits..."
else
  puts "No mobile apps found in database"
end

puts "\n✓ Migration test completed successfully!"
puts "\nNext steps:"
puts "1. Ensure SensorTower API credentials are configured"
puts "2. Run the job with: PopulateMobileAppDataJob.perform_now"
puts "3. Monitor logs for any issues"
puts "4. Batch processing will handle up to 20 apps per API call"
puts "5. Individual app fallback is available if batch processing fails"
