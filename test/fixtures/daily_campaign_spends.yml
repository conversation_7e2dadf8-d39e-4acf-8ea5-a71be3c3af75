# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  spend_date: 2025-06-20
  legacy_client_id: 101
  legacy_click_url_id: 401
  legacy_campaign_id: 301
  legacy_partner_id: 201
  gross_spend: 125.50
  net_spend: 112.95
  net_spend_adjustment: 0.00
  adjusted_net_spend: 112.95
  margin: 12.55
  adjusted_margin: 12.55
  revenue: 125.50
  gross_campaign_spend_id: 1001
  net_campaign_spend_id: 2001
  net_spend_adjustments: 
  is_test: false
  gross_spend_source: "API_IMPORT"
  net_spend_source: "PARTNER_REPORT"
  impression_count: 15420
  click_count: 342
  install_count: 87
  cvr: 25.44

two:
  spend_date: 2025-06-21
  legacy_client_id: 102
  legacy_click_url_id: 402
  legacy_campaign_id: 302
  legacy_partner_id: 202
  gross_spend: 89.75
  net_spend: 80.78
  net_spend_adjustment: -5.25
  adjusted_net_spend: 75.53
  margin: 8.97
  adjusted_margin: 14.22
  revenue: 89.75
  gross_campaign_spend_id: 1002
  net_campaign_spend_id: 2002
  net_spend_adjustments: "fraud_adjustment"
  is_test: false
  gross_spend_source: "MANUAL_ENTRY"
  net_spend_source: "CSV_UPLOAD"
  impression_count: 8950
  click_count: 178
  install_count: 42
  cvr: 23.60

three:
  spend_date: 2025-06-19
  legacy_client_id: 101
  legacy_click_url_id: 401
  legacy_campaign_id: 301
  legacy_partner_id: 203
  gross_spend: 245.80
  net_spend: 221.22
  net_spend_adjustment: 12.50
  adjusted_net_spend: 233.72
  margin: 24.58
  adjusted_margin: 12.08
  revenue: 245.80
  gross_campaign_spend_id: 1003
  net_campaign_spend_id: 2003
  net_spend_adjustments: "bonus_payout"
  is_test: false
  gross_spend_source: "API_IMPORT"
  net_spend_source: "API_IMPORT"
  impression_count: 32150
  click_count: 895
  install_count: 156
  cvr: 17.43

test_data:
  spend_date: 2025-06-21
  legacy_client_id: 103
  legacy_click_url_id: 401
  legacy_campaign_id: 301
  legacy_partner_id: 201
  gross_spend: 0.99
  net_spend: 0.89
  net_spend_adjustment: 0.00
  adjusted_net_spend: 0.89
  margin: 0.10
  adjusted_margin: 0.10
  revenue: 0.99
  gross_campaign_spend_id: 9999
  net_campaign_spend_id: 9999
  net_spend_adjustments: 
  is_test: true
  gross_spend_source: "TEST_DATA"
  net_spend_source: "TEST_DATA"
  impression_count: 100
  click_count: 5
  install_count: 1
  cvr: 20.00
