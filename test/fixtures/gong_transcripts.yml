# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

transcript_one_segment_one:
  gong_call: call_one
  speaker_id: "spk_john_doe"
  speaker_name: "<PERSON>"
  speaker_email: "<EMAIL>"
  speaker_affiliation: "client"
  speaker_title: "Marketing Director"
  text: "Thanks for taking the time to meet with us today. We're really excited about the potential partnership with Feedmob."
  start_time: <%= 2.days.ago %>
  end_time: <%= 2.days.ago + 15.seconds %>
  duration_ms: 15000
  sequence_order: 1
  word_count: 18
  topic: "Introduction"
  metadata: '{"confidence": 0.95, "language": "en"}'

transcript_one_segment_two:
  gong_call: call_one
  speaker_id: "spk_sarah_smith"
  speaker_name: "<PERSON>"
  speaker_email: "<EMAIL>"
  speaker_affiliation: "internal"
  speaker_title: "Account Manager"
  text: "Absolutely, <PERSON>. We've reviewed your app and think we can drive some great results. Let's talk about your target KPIs and budget expectations."
  start_time: <%= 2.days.ago + 16.seconds %>
  end_time: <%= 2.days.ago + 35.seconds %>
  duration_ms: 19000
  sequence_order: 2
  word_count: 24
  topic: "KPI Discussion"
  metadata: '{"confidence": 0.92, "language": "en"}'

transcript_two_segment_one:
  gong_call: call_two
  speaker_id: "spk_mike_johnson"
  speaker_name: "Mike Johnson"
  speaker_email: "<EMAIL>"
  speaker_affiliation: "partner"
  speaker_title: "Business Development Manager"
  text: "Our platform has been performing really well for similar apps in your vertical. We're seeing 15-20% better conversion rates compared to other networks."
  start_time: <%= 1.day.ago %>
  end_time: <%= 1.day.ago + 22.seconds %>
  duration_ms: 22000
  sequence_order: 1
  word_count: 26
  topic: "Performance Metrics"
  metadata: '{"confidence": 0.88, "language": "en"}'

transcript_two_segment_two:
  gong_call: call_two
  speaker_id: "spk_alice_brown"
  speaker_name: "Alice Brown"
  speaker_email: "<EMAIL>"
  speaker_affiliation: "internal"
  speaker_title: "Partnership Manager"
  text: "That's impressive, Mike. Can you walk us through your fraud prevention measures and how you ensure traffic quality?"
  start_time: <%= 1.day.ago + 23.seconds %>
  end_time: <%= 1.day.ago + 38.seconds %>
  duration_ms: 15000
  sequence_order: 2
  word_count: 20
  topic: "Fraud Prevention"
  metadata: '{"confidence": 0.94, "language": "en"}'

transcript_three_segment_one:
  gong_call: call_three
  speaker_id: "spk_lisa_wilson"
  speaker_name: "Lisa Wilson"
  speaker_email: "<EMAIL>"
  speaker_affiliation: "client"
  speaker_title: "Growth Lead"
  text: "We've been running the campaign for two weeks now and wanted to review the performance data with you."
  start_time: <%= 3.hours.ago %>
  end_time: <%= 3.hours.ago + 18.seconds %>
  duration_ms: 18000
  sequence_order: 1
  word_count: 18
  topic: "Campaign Review"
  metadata: '{"confidence": 0.97, "language": "en"}'

transcript_three_segment_two:
  gong_call: call_three
  speaker_id: "spk_tom_davis"
  speaker_name: "Tom Davis"
  speaker_email: "<EMAIL>"
  speaker_affiliation: "internal"
  speaker_title: "Campaign Manager"
  text: "Great! I have the latest numbers here. We're seeing a 12% install conversion rate, which is above your target of 10%. The cost per install is coming in at $2.50."
  start_time: <%= 3.hours.ago + 19.seconds %>
  end_time: <%= 3.hours.ago + 45.seconds %>
  duration_ms: 26000
  sequence_order: 2
  word_count: 32
  topic: "Performance Results"
  metadata: '{"confidence": 0.91, "language": "en"}'
