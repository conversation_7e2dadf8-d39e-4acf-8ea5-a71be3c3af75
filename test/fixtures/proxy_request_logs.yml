# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  api_playground: one
  request_url: "https://api.example.com/test"
  request_method: "GET"
  request_headers: '{"Content-Type": "application/json", "Authorization": "Bearer token"}'
  request_body: '{"param": "value", "test": true}'
  response_status: 200
  response_headers: '{"Content-Type": "application/json", "Server": "nginx"}'
  response_body: '{"result": "success", "data": [1, 2, 3]}'
  succeeded: true
  error_message: null
  requested_at: <%= 1.hour.ago %>

two:
  api_playground: two
  request_url: "https://api.example.com/error"
  request_method: "POST"
  request_headers: '{"Content-Type": "application/json"}'
  request_body: '{"action": "test"}'
  response_status: 500
  response_headers: '{"Content-Type": "application/json"}'
  response_body: '{"error": "Internal Server Error"}'
  succeeded: false
  error_message: "Server returned 500 status"
  requested_at: <%= 30.minutes.ago %>

three:
  api_playground: one
  request_url: "https://api.example.com/timeout"
  request_method: "GET"
  request_headers: '{}'
  request_body: null
  response_status: null
  response_headers: null
  response_body: null
  succeeded: false
  error_message: "Request timeout"
  requested_at: <%= 15.minutes.ago %>
