# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

utterance_one_1:
  fireflies_meeting: one
  sentence_index: 1
  speaker_id: "speaker_001"
  speaker_name: "<PERSON>"
  content: "Thanks everyone for joining today's Q4 strategy session. Let's start by reviewing our current campaign performance."
  start_time: <%= 1.week.ago + 5.minutes %>
  end_time: <%= 1.week.ago + 5.minutes + 8.seconds %>
  is_useful: true

utterance_one_2:
  fireflies_meeting: one
  sentence_index: 2
  speaker_id: "speaker_002"
  speaker_name: "<PERSON>"
  content: "Absolutely, <PERSON>. Our mobile advertising campaigns have shown a 15% increase in install rates compared to last quarter."
  start_time: <%= 1.week.ago + 5.minutes + 10.seconds %>
  end_time: <%= 1.week.ago + 5.minutes + 18.seconds %>
  is_useful: true

utterance_one_3:
  fireflies_meeting: one
  sentence_index: 3
  speaker_id: "speaker_001"
  speaker_name: "<PERSON>"
  content: "That's excellent news. What's driving this improvement? Is it specific partners or creative optimization?"
  start_time: <%= 1.week.ago + 6.minutes + 2.seconds %>
  end_time: <%= 1.week.ago + 6.minutes + 8.seconds %>
  is_useful: false

utterance_two_1:
  fireflies_meeting: two
  sentence_index: 1
  speaker_id: "speaker_003"
  speaker_name: "<PERSON> <PERSON>"
  content: "Hi Bob, I wanted to discuss the recent API integration challenges we've been experiencing with our platform."
  start_time: <%= 3.days.ago + 2.minutes %>
  end_time: <%= 3.days.ago + 2.minutes + 6.seconds %>
  is_useful: true

utterance_two_2:
  fireflies_meeting: two
  sentence_index: 2
  speaker_id: "speaker_004"
  speaker_name: "Bob <PERSON>"
  content: "Yes, Alice. We've identified some latency issues in the attribution window calculations. Let me walk through the technical details."
  start_time: <%= 3.days.ago + 2.minutes + 8.seconds %>
  end_time: <%= 3.days.ago + 2.minutes + 15.seconds %>
  is_useful: true

utterance_two_3:
  fireflies_meeting: two
  sentence_index: 3
  speaker_id: "speaker_003"
  speaker_name: "Alice Johnson"
  content: "That would be helpful. We're particularly concerned about the impact on real-time reporting accuracy."
  start_time: <%= 3.days.ago + 3.minutes + 20.seconds %>
  end_time: <%= 3.days.ago + 3.minutes + 26.seconds %>
  is_useful: false

utterance_three_1:
  fireflies_meeting: three
  sentence_index: 1
  speaker_id: "speaker_005"
  speaker_name: "Carol Davis"
  content: "Dave, thank you for preparing this comprehensive performance analysis. The metrics look quite promising overall."
  start_time: <%= 5.days.ago + 1.minute %>
  end_time: <%= 5.days.ago + 1.minute + 7.seconds %>
  is_useful: false

utterance_three_2:
  fireflies_meeting: three
  sentence_index: 2
  speaker_id: "speaker_006"
  speaker_name: "Dave Brown"
  content: "Thanks, Carol. While most campaigns are performing well, we've identified some areas where we can optimize conversion rates further."
  start_time: <%= 5.days.ago + 1.minute + 10.seconds %>
  end_time: <%= 5.days.ago + 1.minute + 17.seconds %>
  is_useful: true

utterance_three_3:
  fireflies_meeting: three
  sentence_index: 3
  speaker_id: "speaker_005"
  speaker_name: "Carol Davis"
  content: "Excellent. What specific optimizations are you recommending for the underperforming segments?"
  start_time: <%= 5.days.ago + 2.minutes + 5.seconds %>
  end_time: <%= 5.days.ago + 2.minutes + 10.seconds %>
  is_useful: true