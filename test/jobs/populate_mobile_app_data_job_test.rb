require "test_helper"

class PopulateMobileAppDataJobTest < ActiveJob::TestCase
  setup do
    @ios_app = mobile_apps(:two)  # iOS app from fixtures
    @android_app = mobile_apps(:one)  # Android app from fixtures
  end

  test "processes iOS app with SensorTower data" do
    # Mock SensorTower client response
    mock_sensor_tower_response = {
      "apps" => [
        {
          "app_id" => @ios_app.bundle_id,
          "name" => "Test iOS App",
          "icon_url" => "https://example.com/icon.png",
          "description" => "Test description",
          "rating" => 4.5,
          "rating_count" => 1000,
          "categories" => [ "Games", "Action" ],
          "store_url" => "https://apps.apple.com/app/id#{@ios_app.bundle_id}",
          "publisher" => "Test Publisher"
        }
      ]
    }

    # Mock the SensorTower client
    mock_client = Minitest::Mock.new
    mock_client.expect(:get_ios_apps, mock_sensor_tower_response, [ [ @ios_app.bundle_id ], { country: "US" } ])

    job = PopulateMobileAppDataJob.new
    job.stubs(:sensor_tower_client).returns(mock_client)

    # Perform the job
    job.send(:process_ios_app, @ios_app)

    # Verify the mock was called
    mock_client.verify
  end

  test "processes Android app with SensorTower data" do
    # Mock SensorTower client response
    mock_sensor_tower_response = {
      "apps" => [
        {
          "app_id" => @android_app.bundle_id,
          "name" => "Test Android App",
          "icon_url" => "https://example.com/icon.png",
          "description" => "Test description",
          "rating" => 4.2,
          "rating_count" => 500,
          "categories" => [ "Social", "Communication" ],
          "store_url" => "https://play.google.com/store/apps/details?id=#{@android_app.bundle_id}",
          "publisher" => "Test Publisher"
        }
      ]
    }

    # Mock the SensorTower client
    mock_client = Minitest::Mock.new
    mock_client.expect(:get_android_apps, mock_sensor_tower_response, [ [ @android_app.bundle_id ], { country: "US" } ])

    job = PopulateMobileAppDataJob.new
    job.stubs(:sensor_tower_client).returns(mock_client)

    # Perform the job
    job.send(:process_android_app, @android_app)

    # Verify the mock was called
    mock_client.verify
  end

  test "handles SensorTower API errors gracefully" do
    # Mock the SensorTower client to raise an error
    mock_client = Minitest::Mock.new
    mock_client.expect(:get_ios_apps, proc { raise SensorTower::Client::APIError, "API Error" }, [ [ @ios_app.bundle_id ], { country: "US" } ])

    job = PopulateMobileAppDataJob.new
    job.stubs(:sensor_tower_client).returns(mock_client)

    # Should not raise an error, should return nil
    result = job.send(:process_ios_app, @ios_app)
    assert_nil result

    # Verify the mock was called
    mock_client.verify
  end
end
