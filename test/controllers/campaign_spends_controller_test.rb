require "test_helper"

class CampaignSpendsControllerTest < ActionDispatch::IntegrationTest
  setup do
    login
    RefreshCampaignSpendSummariesJob.perform_now
  end

  test "should get default api response" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19"
    }

    target = {
      "2024-12-19" => { "gross" => 2.99, "net" => 7.97 },
      "2024-12-20" => { "gross" => 8.98, "net" => 6.0 },
      "2024-12-21" => { "gross" => 14.98, "net" => 4.99 }
    }

    assert_equal target, response.parsed_body.sort.to_h
  end

  test "should get default api response with client filter" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      group_by: "client",
      legacy_client_id_in: [ clients(:one).legacy_id ]
    }

    assert_equal response.parsed_body, {
      "One" => { "gross" => 13.97, "net" => 13.48 }
    }
  end

  test "should get partner level api response" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      group_by: "partner"
    }

    assert_equal response.parsed_body, {
      "One" => { "gross" => 13.97, "net" => 13.48 },
      "Two" => { "gross" => 12.98, "net" => 5.48 }
    }
  end

  test "should get partner level api response with partner filter" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      group_by: "partner",
      legacy_partner_id_in: [ partners(:one).legacy_id ]
    }
    assert_equal response.parsed_body, {
      "One" => { "gross" => 13.97, "net" => 13.48 }
    }
  end

  test "should get campaign level api response" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      group_by: "campaign"
    }
    assert_equal response.parsed_body, {
      "ONE_iOS_US" => { "gross" => 13.97, "net" => 13.48 },
      "TWO_Android_UK" => { "gross" => 12.98, "net" => 5.48 }
    }
  end

  test "should get campaign level api response with campaign filter" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      group_by: "campaign",
      legacy_campaign_id_in: [ campaigns(:one).legacy_id ]
    }
    assert_equal response.parsed_body, {
      "ONE_iOS_US" => { "gross" => 13.97, "net" => 13.48 }
    }
  end

  test "should get click url level api response" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      group_by: "click_url"
    }
    assert_equal response.parsed_body, {
      "401" => { "gross" => 13.97, "net" => 13.48 },
      "402" => { "gross" => 12.98, "net" => 5.48 }
    }
  end

  test "should get click url level api response with click url filter" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      group_by: "click_url",
      legacy_click_url_id_in: [ click_urls(:one).legacy_id ]
    }
    assert_equal response.parsed_body, {
      "401" => { "gross" => 13.97, "net" => 13.48 }
    }
  end

  test "should get daily api response" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      group_by: "day"
    }
    assert_equal response.parsed_body, {
      "2024-12-21" => { "gross" => 14.98, "net" => 4.99 },
      "2024-12-20" => { "gross" => 8.98, "net" => 6.0 },
      "2024-12-19" => { "gross" => 2.99, "net" => 7.97 }
    }
  end

  test "should get weekly api response" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      group_by: "week"
    }
    assert_equal response.parsed_body, {
      "2024-W51" => { "gross" => 26.95, "net" => 18.96 }
    }
  end

  test "should get monthly api response" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      group_by: "month"
    }
    assert_equal response.parsed_body, {
      "2024-12" => { "gross" => 26.95, "net" => 18.96 }
    }
  end

  test "date overflow is allowed" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      date_lteq: "2024-12-32",
      group_by: "month"
    }
    assert_equal response.parsed_body, {
      "2024-12" => { "gross" => 26.95, "net" => 18.96 }
    }
  end

  test "should get customized metrics api response" do
    get campaign_spends_url(format: :json), params: {
      date_gteq: "2024-12-19",
      metrics: [ "gross", "revenue" ]
    }
    assert_equal response.parsed_body, {
      "2024-12-21" => { "gross" => 14.98, "revenue" => 9.99 },
      "2024-12-20" => { "gross" => 8.98, "revenue" => 2.98 },
      "2024-12-19" => { "gross" => 2.99, "revenue" => -4.98 }
    }
  end

  test "should get CSV response with correct filename for single date" do
    query_params = {
      date_gteq: "2024-12-20",
      date_lteq: "2024-12-20"
    }
    get campaign_spends_url(format: :csv), params: { query: query_params }

    assert_response :success
    assert_equal "text/csv", response.content_type
    assert_match(%r{campaign-spends-2024-12-20\.csv}, response.headers["Content-Disposition"])
    csv = CSV.parse(response.body)
    assert_equal [ "Spend Date", "Client Name", "Campaign Name", "Vendor Name", "Click URL ID", "Gross Spend", "Net Spend", "Revenue", "Margin (%)", "Impressions", "Clicks", "Installs" ], csv.first
  end

  test "should get CSV response with correct filename for date range" do
    query_params = {
      date_gteq: "2023-01-01",
      date_lteq: "2023-01-02"
    }
    get campaign_spends_url(format: :csv), params: { query: query_params }

    assert_response :success
    assert_equal "text/csv", response.content_type
    assert_match(/campaign-spends-2023-01-01_2023-01-02\.csv/, response.headers["Content-Disposition"])
    csv_lines = response.body.split("\n")
    assert_equal "Spend Date,Client Name,Campaign Name,Vendor Name,Click URL ID,Gross Spend,Net Spend,Revenue,Margin (%),Impressions,Clicks,Installs", csv_lines[0]
    assert_equal "2023-01-02,One,ONE_iOS_US,One,401,3.60,2.40,1.2,33.33,15000,5000,25", csv_lines[1]
    assert_equal "2023-01-01,Two,TWO_Android_UK,Two,402,4.50,3.50,1.0,22.22,5000,600,15", csv_lines[2]
  end

  test "should get CSV response with customized fields" do
    query_params = {
      date_gteq: "2023-01-01",
      date_lteq: "2023-01-02",
      export_fields: [ "date", "client_name", "vendor_name", "campaign_name", "click_url_legacy_id", "gross_spend", "net_spend", "revenue" ]
    }
    get campaign_spends_url(format: :csv), params: { query: query_params }
    assert_response :success
    assert_equal "text/csv", response.content_type
    assert_match(%r{campaign-spends-2023-01-01_2023-01-02\.csv}, response.headers["Content-Disposition"])
    csv_lines = response.body.split("\n")
    assert_equal "Spend Date,Client Name,Vendor Name,Campaign Name,Click URL ID,Gross Spend,Net Spend,Revenue", csv_lines[0]
  end

  test "should get CSV response with no data" do
    query_params = {
      date_gteq: "2022-01-01",
      date_lteq: "2022-01-02",
      export_fields: [ "date", "client_name", "vendor_name", "campaign_name", "click_url_legacy_id", "gross_spend", "net_spend", "revenue" ]
    }
    get campaign_spends_url(format: :csv), params: { query: query_params }
    assert_response :success
    assert_equal "text/csv", response.content_type
    assert_match(%r{campaign-spends-2022-01-01_2022-01-02\.csv}, response.headers["Content-Disposition"])
    csv_lines = response.body.split("\n")
    assert_equal "Spend Date,Client Name,Vendor Name,Campaign Name,Click URL ID,Gross Spend,Net Spend,Revenue", csv_lines[0]
    assert_nil csv_lines[1]
  end

  test "should export aggregated CSV with valid parameters" do
    get export_aggregated_campaign_spends_path(format: :csv), params: {
      query: {
        export_type: "client",
        date_gteq: "2024-12-19",
        date_lteq: "2024-12-30"
      }
    }

    assert_response :success
    assert_equal "text/csv", response.content_type
    assert_match(/attachment; filename="clients-2024-12-19_2024-12-30.csv"/, response.headers["Content-Disposition"])
    assert_not_nil response.body
    assert_includes response.body, "Client"
    assert_includes response.body, "Total Gross Spend"
    csv_lines = response.body.split("\n")
    assert_equal "Client,Total Gross Spend,Total Net Spend,Revenues,Margin (%),Impressions,Clicks,Installs", csv_lines[0]
    assert_equal "One,13.97,13.48,0.49,3.51,10000,5500,25", csv_lines[1]
  end

  test "filename should contain single date when dates are equal" do
    get export_aggregated_campaign_spends_path(format: :csv), params: {
      query: {
        export_type: "client",
        date_gteq: "2024-12-19",
        date_lteq: "2024-12-19"
      }
    }

    assert_match(/clients-2024-12-19\.csv/, response.headers["Content-Disposition"])
    csv_lines = response.body.split("\n")
    second_line = csv_lines[1].split(",")
    assert_equal "One", second_line[0]
    assert_equal "2.99", second_line[1]
    assert_equal "5.99", second_line[2]
  end

  test "should return error for invalid export type" do
    get export_aggregated_campaign_spends_path(format: :csv), params: {
      query: {
        export_type: "invalid_type",
        date_gteq: "2024-12-19",
        date_lteq: "2025-01-31"
      }
    }

    json_response = JSON.parse(response.body)
    assert_equal "Invalid export configuration. Please check your export type and try again.", json_response["error"]
  end

  test "should get stats" do
    get stats_campaign_spends_url, params: {
      query: {
        date_gteq: "2024-12-19",
        date_lteq: "2024-12-21"
      }
    }

    assert_response :success
    assert_not_nil assigns(:partner_spend)
    assert_not_nil assigns(:client_spend)
    assert_not_nil assigns(:revenue)
    assert_not_nil assigns(:adjusted_net_spend)
    assert_not_nil assigns(:margin)
    assert_equal Date.parse("2024-12-19"), assigns(:start_date)
    assert_equal Date.parse("2024-12-21"), assigns(:end_date)
  end
end
