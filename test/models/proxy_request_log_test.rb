require "test_helper"

class ProxyRequestLogTest < ActiveSupport::TestCase
  def setup
    @api_playground = api_playgrounds(:one)
    @proxy_request_log = ProxyRequestLog.new(
      api_playground: @api_playground,
      request_url: "https://api.example.com/test",
      request_method: "GET",
      request_headers: { "Content-Type" => "application/json" },
      request_body: { "param" => "value" },
      response_status: 200,
      response_headers: { "Content-Type" => "application/json" },
      response_body: '{"result": "success"}',
      succeeded: true,
      requested_at: Time.current
    )
  end

  test "should be valid with valid attributes" do
    assert @proxy_request_log.valid?
  end

  test "should belong to api_playground" do
    assert_respond_to @proxy_request_log, :api_playground
    assert_instance_of ApiPlayground, @proxy_request_log.api_playground
  end

  test "should require api_playground" do
    @proxy_request_log.api_playground = nil
    assert_not @proxy_request_log.valid?
    assert_includes @proxy_request_log.errors[:api_playground], "must exist"
  end

  test "should save with minimal required attributes" do
    minimal_log = ProxyRequestLog.new(api_playground: @api_playground)
    assert minimal_log.save
  end

  test "should store request_headers as jsonb" do
    @proxy_request_log.save!
    assert_equal({ "Content-Type" => "application/json" }, @proxy_request_log.request_headers)
  end

  test "should store request_body as jsonb" do
    @proxy_request_log.save!
    assert_equal({ "param" => "value" }, @proxy_request_log.request_body)
  end

  test "should store response_headers as jsonb" do
    @proxy_request_log.save!
    assert_equal({ "Content-Type" => "application/json" }, @proxy_request_log.response_headers)
  end

  test "should handle nil jsonb fields" do
    log = ProxyRequestLog.create!(
      api_playground: @api_playground,
      request_headers: nil,
      request_body: nil,
      response_headers: nil
    )
    assert_nil log.request_headers
    assert_nil log.request_body
    assert_nil log.response_headers
  end

  test "should store response_status as integer" do
    @proxy_request_log.response_status = 404
    @proxy_request_log.save!
    assert_equal 404, @proxy_request_log.response_status
  end

  test "should store succeeded as boolean" do
    @proxy_request_log.succeeded = false
    @proxy_request_log.save!
    assert_equal false, @proxy_request_log.succeeded
  end

  test "should store error_message as string" do
    error_msg = "Connection timeout"
    @proxy_request_log.error_message = error_msg
    @proxy_request_log.save!
    assert_equal error_msg, @proxy_request_log.error_message
  end

  test "should store requested_at as datetime" do
    time = 1.hour.ago
    @proxy_request_log.requested_at = time
    @proxy_request_log.save!
    assert_in_delta time.to_f, @proxy_request_log.requested_at.to_f, 1.0
  end

  test "should have timestamps" do
    @proxy_request_log.save!
    assert_not_nil @proxy_request_log.created_at
    assert_not_nil @proxy_request_log.updated_at
  end

  test "should be destroyed when api_playground is destroyed" do
    @proxy_request_log.save!
    log_id = @proxy_request_log.id

    @api_playground.destroy

    assert_raises(ActiveRecord::RecordNotFound) do
      ProxyRequestLog.find(log_id)
    end
  end

  test "should handle complex jsonb data" do
    complex_data = {
      "nested" => {
        "array" => [ 1, 2, 3 ],
        "hash" => { "key" => "value" }
      },
      "boolean" => true,
      "number" => 42
    }

    @proxy_request_log.request_body = complex_data
    @proxy_request_log.save!

    assert_equal complex_data, @proxy_request_log.reload.request_body
  end
end
