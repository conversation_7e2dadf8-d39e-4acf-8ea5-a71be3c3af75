require "test_helper"

class SensorTower::AppDataAdapterTest < ActiveSupport::TestCase
  setup do
    @ios_sensor_tower_data = {
      "apps" => [
        {
          "app_id" => "284910350",
          "name" => "Test iOS App",
          "icon_url" => "https://example.com/icon.png",
          "description" => "Test iOS app description",
          "rating" => 4.5,
          "rating_count" => 1000,
          "categories" => ["Games", "Action"],
          "store_url" => "https://apps.apple.com/app/id284910350",
          "publisher" => "Test Publisher"
        }
      ]
    }

    @android_sensor_tower_data = {
      "apps" => [
        {
          "app_id" => "com.test.app",
          "name" => "Test Android App",
          "icon_url" => "https://example.com/android_icon.png",
          "description" => "Test Android app description",
          "rating" => 4.2,
          "rating_count" => 500,
          "categories" => ["Social", "Communication"],
          "store_url" => "https://play.google.com/store/apps/details?id=com.test.app",
          "publisher" => "Test Android Publisher"
        }
      ]
    }
  end

  test "creates adapter for iOS app with valid data" do
    adapter = SensorTower::AppDataAdapter.new(@ios_sensor_tower_data, "284910350", "ios")

    assert_equal "284910350", adapter.app_id
    assert_equal "Test iOS App", adapter.name
    assert_equal "https://example.com/icon.png", adapter.artwork
    assert_equal "Test iOS app description", adapter.description
    assert_equal 4.5, adapter.current_rating
    assert_equal 1000, adapter.rating_count
    assert_equal ["Games", "Action"], adapter.genre_names
    assert_equal "Games", adapter.primary_genre
    assert_equal "https://apps.apple.com/app/id284910350", adapter.url
    assert_equal "Test Publisher", adapter.author
    assert_equal :app_store, adapter.store_type
  end

  test "creates adapter for Android app with valid data" do
    adapter = SensorTower::AppDataAdapter.new(@android_sensor_tower_data, "com.test.app", "android")

    assert_equal "com.test.app", adapter.app_id
    assert_equal "Test Android App", adapter.name
    assert_equal "https://example.com/android_icon.png", adapter.artwork
    assert_equal "Test Android app description", adapter.description
    assert_equal 4.2, adapter.current_rating
    assert_equal 500, adapter.rating_count
    assert_equal ["Social", "Communication"], adapter.genre_names
    assert_equal "Social", adapter.primary_genre
    assert_equal "https://play.google.com/store/apps/details?id=com.test.app", adapter.url
    assert_equal "Test Android Publisher", adapter.author
    assert_equal :google_play, adapter.store_type
  end

  test "raises error when app not found in data" do
    assert_raises(SensorTower::AppDataAdapter::AppNotFoundError) do
      SensorTower::AppDataAdapter.new(@ios_sensor_tower_data, "nonexistent_id", "ios")
    end
  end

  test "handles empty categories gracefully" do
    data_without_categories = {
      "apps" => [
        {
          "app_id" => "284910350",
          "name" => "Test App",
          "categories" => nil
        }
      ]
    }

    adapter = SensorTower::AppDataAdapter.new(data_without_categories, "284910350", "ios")
    assert_equal [], adapter.genre_names
    assert_nil adapter.primary_genre
  end

  test "converts to hash correctly" do
    adapter = SensorTower::AppDataAdapter.new(@ios_sensor_tower_data, "284910350", "ios")
    hash = adapter.to_hash

    expected_keys = [:id, :name, :artwork, :description, :current_rating, :rating_count,
                     :genre_names, :primary_genre, :url, :price, :author, :store_type]
    
    assert_equal expected_keys.sort, hash.keys.sort
    assert_equal "284910350", hash[:id]
    assert_equal "Test iOS App", hash[:name]
    assert_equal :app_store, hash[:store_type]
  end

  test "converts to JSON correctly" do
    adapter = SensorTower::AppDataAdapter.new(@ios_sensor_tower_data, "284910350", "ios")
    json_string = adapter.to_json
    parsed_json = JSON.parse(json_string)

    assert_equal "284910350", parsed_json["id"]
    assert_equal "Test iOS App", parsed_json["name"]
    assert_equal "app_store", parsed_json["store_type"]
  end

  test "builds store URL when not provided" do
    data_without_url = {
      "apps" => [
        {
          "app_id" => "284910350",
          "name" => "Test App",
          "store_url" => nil
        }
      ]
    }

    ios_adapter = SensorTower::AppDataAdapter.new(data_without_url, "284910350", "ios")
    assert_equal "https://apps.apple.com/app/id284910350", ios_adapter.url

    data_without_url["apps"][0]["app_id"] = "com.test.app"
    android_adapter = SensorTower::AppDataAdapter.new(data_without_url, "com.test.app", "android")
    assert_equal "https://play.google.com/store/apps/details?id=com.test.app", android_adapter.url
  end
end
